#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从页面直接提取视频链接
"""

import requests
from config import COOKIES
import re
import json
from bs4 import BeautifulSoup
from acc5_simple import authcode_decrypt

def extract_video_links_from_page(course_id="15361", lesson_id="94601"):
    """从课程页面直接提取视频链接"""
    session = requests.Session()
    
    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
    }
    
    # 建立会话
    print("🔄 建立会话...")
    session.get("https://m.acc5.com/home/", headers=headers, cookies=COOKIES)
    
    # 访问具体的课程学习页面
    lesson_url = f"https://m.acc5.com/course/course_{course_id}/learn/lesson_{lesson_id}/"
    print(f"🔄 访问课程页面: {lesson_url}")
    
    response = session.get(lesson_url, headers=headers, cookies=COOKIES)
    print(f"📊 页面状态: {response.status_code}")
    
    if response.status_code != 200:
        print("❌ 无法访问课程页面")
        return []
    
    page_content = response.text
    print(f"📄 页面大小: {len(page_content)} 字符")
    
    # 保存页面内容用于调试
    with open('page_content.html', 'w', encoding='utf-8') as f:
        f.write(page_content)
    print("💾 页面内容已保存到 page_content.html")
    
    video_links = []
    
    # 1. 使用正则表达式查找各种视频链接格式
    print("\n🔍 使用正则表达式搜索视频链接...")
    
    video_patterns = [
        # m3u8 链接
        r'https?://[^"\s]+\.m3u8[^"\s]*',
        r'["\']([^"\']*\.m3u8[^"\']*)["\']',
        
        # mp4 链接
        r'https?://[^"\s]+\.mp4[^"\s]*',
        r'["\']([^"\']*\.mp4[^"\']*)["\']',
        
        # 其他视频格式
        r'https?://[^"\s]+\.(avi|mov|wmv|flv|webm)[^"\s]*',
        
        # 可能的视频服务器域名
        r'https?://[^"\s]*video[^"\s]*\.(com|cn|net)[^"\s]*',
        r'https?://[^"\s]*stream[^"\s]*\.(com|cn|net)[^"\s]*',
        r'https?://[^"\s]*media[^"\s]*\.(com|cn|net)[^"\s]*',
        
        # 加密的视频链接模式
        r'video[_-]?url["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        r'stream[_-]?url["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        r'play[_-]?url["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        r'src["\']?\s*[:=]\s*["\']([^"\']*\.(m3u8|mp4)[^"\']*)["\']',
    ]
    
    for pattern in video_patterns:
        matches = re.findall(pattern, page_content, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple):
                match = match[0]  # 提取第一个捕获组
            if match and match not in video_links:
                video_links.append(match)
                print(f"  ✅ 找到: {match}")
    
    # 2. 查找JavaScript变量中的视频链接
    print("\n🔍 搜索JavaScript变量...")
    
    js_patterns = [
        r'var\s+videoUrl\s*=\s*["\']([^"\']+)["\']',
        r'var\s+streamUrl\s*=\s*["\']([^"\']+)["\']',
        r'var\s+playUrl\s*=\s*["\']([^"\']+)["\']',
        r'videoUrl\s*:\s*["\']([^"\']+)["\']',
        r'streamUrl\s*:\s*["\']([^"\']+)["\']',
        r'playUrl\s*:\s*["\']([^"\']+)["\']',
        r'url\s*:\s*["\']([^"\']*\.(m3u8|mp4)[^"\']*)["\']',
    ]
    
    for pattern in js_patterns:
        matches = re.findall(pattern, page_content, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple):
                match = match[0]  # 提取第一个捕获组
            if match and match not in video_links:
                video_links.append(match)
                print(f"  ✅ JS变量: {match}")
    
    # 3. 查找可能的加密字符串
    print("\n🔍 搜索可能的加密字符串...")
    
    # 查找长的Base64字符串（可能是加密的视频链接）
    base64_pattern = r'[A-Za-z0-9+/]{50,}={0,2}'
    base64_matches = re.findall(base64_pattern, page_content)
    
    for match in base64_matches:
        if len(match) > 100:  # 只处理较长的字符串
            print(f"  🔐 尝试解密: {match[:50]}...")
            
            # 尝试authcode解密
            try:
                decrypted = authcode_decrypt(match)
                if decrypted and ('http' in decrypted or '.m3u8' in decrypted or '.mp4' in decrypted):
                    print(f"  ✅ 解密成功: {decrypted}")
                    if decrypted not in video_links:
                        video_links.append(decrypted)
            except:
                pass
            
            # 尝试Base64解码
            try:
                import base64
                decoded = base64.b64decode(match).decode('utf-8', errors='ignore')
                if 'http' in decoded and ('.m3u8' in decoded or '.mp4' in decoded):
                    print(f"  ✅ Base64解码: {decoded}")
                    if decoded not in video_links:
                        video_links.append(decoded)
            except:
                pass
    
    # 4. 使用BeautifulSoup查找video和source标签
    print("\n🔍 搜索HTML视频标签...")
    
    try:
        soup = BeautifulSoup(page_content, 'html.parser')
        
        # 查找video标签
        video_tags = soup.find_all('video')
        for video in video_tags:
            src = video.get('src')
            if src:
                print(f"  ✅ video标签: {src}")
                if src not in video_links:
                    video_links.append(src)
        
        # 查找source标签
        source_tags = soup.find_all('source')
        for source in source_tags:
            src = source.get('src')
            if src:
                print(f"  ✅ source标签: {src}")
                if src not in video_links:
                    video_links.append(src)
        
        # 查找data-*属性中的视频链接
        for tag in soup.find_all(attrs={'data-video': True}):
            video_url = tag.get('data-video')
            if video_url:
                print(f"  ✅ data-video: {video_url}")
                if video_url not in video_links:
                    video_links.append(video_url)
        
        for tag in soup.find_all(attrs={'data-src': True}):
            src = tag.get('data-src')
            if src and ('.m3u8' in src or '.mp4' in src):
                print(f"  ✅ data-src: {src}")
                if src not in video_links:
                    video_links.append(src)
    
    except Exception as e:
        print(f"  ❌ BeautifulSoup解析失败: {e}")
    
    # 5. 查找JSON数据中的视频链接
    print("\n🔍 搜索JSON数据...")
    
    json_pattern = r'\{[^{}]*(?:"url"|"video"|"stream"|"play")[^{}]*\}'
    json_matches = re.findall(json_pattern, page_content, re.IGNORECASE)
    
    for match in json_matches:
        try:
            data = json.loads(match)
            for key, value in data.items():
                if isinstance(value, str) and ('.m3u8' in value or '.mp4' in value or value.startswith('http')):
                    print(f"  ✅ JSON中的{key}: {value}")
                    if value not in video_links:
                        video_links.append(value)
        except:
            pass
    
    return video_links

def test_video_links(video_links):
    """测试视频链接的有效性"""
    print(f"\n🧪 测试 {len(video_links)} 个视频链接的有效性...")
    
    valid_links = []
    
    for i, link in enumerate(video_links, 1):
        print(f"\n[{i}/{len(video_links)}] 测试: {link}")
        
        try:
            response = requests.head(link, timeout=10)
            print(f"  状态: {response.status_code}")
            print(f"  类型: {response.headers.get('content-type', 'unknown')}")
            print(f"  大小: {response.headers.get('content-length', 'unknown')}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'video' in content_type or 'application/vnd.apple.mpegurl' in content_type:
                    print(f"  ✅ 有效的视频链接!")
                    valid_links.append(link)
                else:
                    print(f"  ⚠️ 可能不是视频文件")
            else:
                print(f"  ❌ 链接无效")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
    
    return valid_links

if __name__ == "__main__":
    print("🎯 从页面直接提取视频链接")
    print("=" * 60)
    
    # 提取视频链接
    video_links = extract_video_links_from_page()
    
    if video_links:
        print(f"\n🎉 找到 {len(video_links)} 个可能的视频链接:")
        for i, link in enumerate(video_links, 1):
            print(f"{i}. {link}")
        
        # 测试链接有效性
        valid_links = test_video_links(video_links)
        
        if valid_links:
            print(f"\n✅ 有效的视频链接:")
            for link in valid_links:
                print(f"  - {link}")
        else:
            print(f"\n⚠️ 没有找到有效的视频链接")
    else:
        print(f"\n❌ 未找到任何视频链接")
        print(f"\n💡 建议:")
        print("1. 检查页面内容是否正确加载")
        print("2. 可能需要JavaScript执行才能获取视频链接")
        print("3. 视频链接可能通过AJAX动态加载")
        print("4. 查看保存的page_content.html文件进行手动分析")
