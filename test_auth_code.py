#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试auth_code生成
"""

import hashlib
import time
import base64
from acc5_simple import md5

def authcode_encrypt(str_data, key='acc5app2015SHdEreIoB6wjpiOZ', expiry=0):
    """Python版本的authcode加密函数"""
    try:
        operation = 'ENCODE'
        ckey_length = 4

        # 生成密钥
        key = md5(key)
        keya = md5(key[:16])
        keyb = md5(key[16:32])

        # 生成keyc (使用当前时间的MD5)
        current_time = str(time.time())
        md5_time = md5(current_time)
        keyc = md5_time[-ckey_length:]

        # 准备要加密的数据
        if expiry:
            expiry_time = int(time.time()) + expiry
        else:
            expiry_time = 0
        
        # 格式化时间戳为10位
        expiry_str = str(expiry_time).zfill(10)
        
        # 构造要加密的字符串
        hash_part = md5(str_data + keyb)[:16]
        data_to_encrypt = expiry_str + hash_part + str_data

        # 生成cryptkey
        cryptkey = keya + md5(keya + keyc)

        # RC4加密
        box = list(range(256))
        rndkey = [ord(cryptkey[i % len(cryptkey)]) for i in range(256)]

        # 初始化密钥流
        j = 0
        for i in range(256):
            j = (j + box[i] + rndkey[i]) % 256
            box[i], box[j] = box[j], box[i]

        # 加密数据
        a = j = 0
        result = []
        for char in data_to_encrypt:
            a = (a + 1) % 256
            j = (j + box[a]) % 256
            box[a], box[j] = box[j], box[a]
            k = box[(box[a] + box[j]) % 256]
            result.append(chr(ord(char) ^ k))

        encrypted_data = ''.join(result)

        # Base64编码
        encoded = base64.b64encode(encrypted_data.encode('latin-1')).decode('ascii')
        
        # 移除填充字符
        encoded = encoded.rstrip('=')
        
        # 添加keyc前缀
        return keyc + encoded

    except Exception as e:
        print(f"authcode加密错误: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

def get_auth_code():
    """生成auth_code"""
    # 模拟JavaScript中的参数
    device_id = "test-device-id-12345"  # 模拟设备ID
    user_id = "17660150"  # 从cookies中的nUserInfo提取的用户ID
    device_model = "iPhone"  # 模拟设备型号
    app_id = "A6999270760613"  # 从JavaScript代码中找到的appId
    
    # 构造deviceid字符串
    deviceid = f"{device_id}\t{user_id}\t{device_model}\t{app_id}"
    deviceid = deviceid.replace('+', '%2B')
    
    print(f"🔍 构造的deviceid: {deviceid}")
    
    # 使用authcode加密
    expiry = 24 * 60 * 60 * 1000  # 24小时（毫秒）
    encrypted = authcode_encrypt(deviceid, 'acc5app2015SHdEreIoB6wjpiOZ', expiry)
    
    if encrypted:
        # Base64编码
        auth_code = base64.b64encode(encrypted.encode('utf-8')).decode('ascii')
        print(f"✅ 生成的auth_code: {auth_code}")
        return auth_code
    else:
        print("❌ auth_code生成失败")
        return None

def test_api_with_auth():
    """使用auth_code测试API"""
    import requests
    from config import COOKIES
    
    auth_code = get_auth_code()
    if not auth_code:
        return
    
    session = requests.Session()
    base_url = "https://www.acc5.com"
    
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
        "x-requested-with": "XMLHttpRequest",
    }
    
    # 测试用户信息API
    print("\n🔍 测试用户信息API...")
    data = {
        'auth_code': auth_code,
        'appVer': '1.9.31',
        'os': 'ios',
        'osVer': '16.6'
    }
    
    response = session.post(
        f"{base_url}/api.php?encrypt=1&c=app_userinfo",
        headers=headers,
        cookies=COOKIES,
        data=data
    )
    
    print(f"📊 响应状态: {response.status_code}")
    if response.status_code == 200:
        try:
            result = response.json()
            print(f"📋 响应数据: {result}")
            
            if result.get('retcode') == 200 and 'result' in result:
                from acc5_simple import authcode_decrypt
                decrypted = authcode_decrypt(result['result'])
                if decrypted:
                    print(f"✅ 解密成功: {decrypted}")
                else:
                    print("❌ 解密失败")
        except Exception as e:
            print(f"❌ 处理响应失败: {e}")
            print(f"📄 原始响应: {response.text[:200]}...")

if __name__ == "__main__":
    print("🧪 测试auth_code生成")
    print("=" * 60)
    
    test_api_with_auth()
