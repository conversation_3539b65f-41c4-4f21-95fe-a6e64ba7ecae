#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会计学堂课程列表获取器 - 简化版
"""

import requests
from bs4 import BeautifulSoup
import time
import re
import hashlib
from config import COOKIES

def md5(string):
    """MD5哈希函数"""
    return hashlib.md5(string.encode('utf-8')).hexdigest()

def custom_base64_decode(s):
    """自定义Base64解码函数，模拟JavaScript版本"""
    base64_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
    base64_decode_chars = [-1] * 256

    # 构建解码表
    for i, char in enumerate(base64_chars):
        base64_decode_chars[ord(char)] = i

    result = []
    i = 0
    length = len(s)

    while i < length:
        # 获取第一个有效字符
        c1 = -1
        while i < length and c1 == -1:
            c1 = base64_decode_chars[ord(s[i]) & 0xff]
            i += 1
        if c1 == -1:
            break

        # 获取第二个有效字符
        c2 = -1
        while i < length and c2 == -1:
            c2 = base64_decode_chars[ord(s[i]) & 0xff]
            i += 1
        if c2 == -1:
            break

        result.append(chr((c1 << 2) | ((c2 & 0x30) >> 4)))

        # 获取第三个字符
        if i < length:
            c3 = ord(s[i]) & 0xff
            i += 1
            if c3 == 61:  # '='
                break
            c3 = base64_decode_chars[c3]
            if c3 != -1:
                result.append(chr(((c2 & 0x0F) << 4) | ((c3 & 0x3C) >> 2)))

                # 获取第四个字符
                if i < length:
                    c4 = ord(s[i]) & 0xff
                    i += 1
                    if c4 == 61:  # '='
                        break
                    c4 = base64_decode_chars[c4]
                    if c4 != -1:
                        result.append(chr(((c3 & 0x03) << 6) | c4))

    return ''.join(result)

def authcode_decrypt(str_data, key='acc5app2015SHdEreIoB6wjpiOZ'):
    """Python版本的authcode解密函数，精确模拟JavaScript版本"""
    try:
        operation = 'DECODE'
        ckey_length = 4

        # 生成密钥
        key = md5(key)
        keya = md5(key[:16])
        keyb = md5(key[16:32])

        # 提取密钥c
        if len(str_data) < ckey_length:
            return None
        keyc = str_data[:ckey_length]
        str_data = str_data[ckey_length:]

        # 使用自定义Base64解码
        try:
            strbuf = custom_base64_decode(str_data)
            print(f"🔍 Base64解码成功，长度: {len(strbuf)}")
        except Exception as e:
            print(f"Base64解码失败: {e}")
            return None

        # 生成cryptkey
        cryptkey = keya + md5(keya + keyc)

        # RC4解密 - 初始化密钥盒
        box = list(range(256))
        rndkey = [ord(cryptkey[i % len(cryptkey)]) for i in range(256)]

        # 打乱密钥盒
        j = 0
        for i in range(256):
            j = (j + box[i] + rndkey[i]) % 256
            box[i], box[j] = box[j], box[i]

        # RC4解密核心部分
        s = []
        a = j = 0
        for i, char in enumerate(strbuf):
            a = (a + 1) % 256
            j = (j + box[a]) % 256
            box[a], box[j] = box[j], box[a]
            k = box[(box[a] + box[j]) % 256]
            s.append(chr(ord(char) ^ k))

        result = ''.join(s)
        print(f"🔍 RC4解密完成，长度: {len(result)}")

        # 验证解密结果
        if len(result) > 26:
            # 提取各部分
            timestamp_part = result[:10]
            hash_part = result[10:26]
            data_part = result[26:]

            # 验证哈希
            expected_hash = md5(data_part + keyb)[:16]

            print(f"🔍 时间戳部分: {repr(timestamp_part)}")
            print(f"🔍 哈希部分: {repr(hash_part)}")
            print(f"🔍 期望哈希: {repr(expected_hash)}")
            print(f"🔍 数据部分: {repr(data_part[:50])}...")

            # 检查时间戳（JavaScript逻辑：timestamp == 0 或 timestamp > current_time）
            try:
                timestamp = int(timestamp_part)
                current_time = int(time.time())
                timestamp_valid = (timestamp == 0 or timestamp > current_time)
                print(f"🔍 时间戳验证: {timestamp} vs {current_time}, 有效: {timestamp_valid}")
            except:
                timestamp_valid = False
                print("🔍 时间戳解析失败")

            # 哈希验证
            hash_valid = (hash_part == expected_hash)
            print(f"🔍 哈希验证: {hash_valid}")

            if timestamp_valid and hash_valid:
                print("✅ 验证通过，返回数据部分")
                return data_part
            else:
                print("⚠️ 验证失败，但仍返回数据部分")
                return data_part
        else:
            print("⚠️ 结果长度不足26，直接返回")
            return result

    except Exception as e:
        print(f"authcode解密错误: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

def get_course_details(course_url):
    """获取课程详情"""
    session = requests.Session()
    base_url = "https://m.acc5.com"

    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "referer": "https://m.acc5.com/home/<USER>",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
    }

    # 先访问个人中心建立会话
    session.get(f"{base_url}/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)

    # 获取课程详情页面
    response = session.get(course_url, headers=headers, cookies=COOKIES)

    if response.status_code != 200:
        print(f"❌ 请求失败: {response.status_code}")
        return None

    html = response.text
    soup = BeautifulSoup(html, 'html.parser')

    # 提取课程基本信息
    course_info = {}

    # 课程标题
    title_elem = soup.find('span', class_='left')
    course_info['title'] = title_elem.get_text(strip=True) if title_elem else '未知课程'

    # 课程统计信息
    count_elem = soup.find('div', class_='msg-is count')
    course_info['total_lessons'] = count_elem.get_text(strip=True) if count_elem else '未知'

    num_elem = soup.find('div', class_='msg-is num')
    course_info['students'] = num_elem.get_text(strip=True) if num_elem else '未知'

    # 课程简介
    intro_elem = soup.find('div', class_='detail-intro')
    course_info['description'] = intro_elem.find('p').get_text(strip=True) if intro_elem else ''

    # 教师信息
    teacher_elem = soup.find('div', class_='teacher-is')
    course_info['teacher'] = teacher_elem.find('span', class_='name').get_text(strip=True) if teacher_elem else '未知'

    # 提取章节列表
    lessons = []
    lesson_elements = soup.find_all('li', class_='son-title')

    for i, lesson in enumerate(lesson_elements, 1):
        # 章节标题
        title_elem = lesson.find('p', class_='title')
        title = title_elem.get_text(strip=True) if title_elem else f'第{i}节'

        # 时长信息
        time_elem = lesson.find('p', class_='time')
        duration = time_elem.get_text(strip=True) if time_elem else '00:00'

        # 是否可试看
        is_trial = '试看' in lesson.get_text()

        # 播放状态
        if 'in-play-icon' in str(lesson):
            status = '🔄 正在播放'
        elif 'play-icon' in str(lesson):
            status = '⏸️ 未开始'
        else:
            status = '✅ 已完成'

        lessons.append({
            'index': i,
            'title': title,
            'duration': duration,
            'is_trial': is_trial,
            'status': status
        })

    course_info['lessons'] = lessons
    return course_info

def get_courses():
    """获取课程列表"""
    session = requests.Session()
    base_url = "https://m.acc5.com"
    url = f"{base_url}/home/<USER>"

    # 请求头
    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "referer": "https://m.acc5.com/home/",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
    }

    # Cookies
    cookies = {
        "Qs_lvt_449068": "**********",
        "li_first_access_time": "**********",
        "li_last_login_time": "**********",
        "Hm_lvt_1e531798c221147143a6d33847c60bc5": "**********,**********,**********,**********",
        "HMACCOUNT": "****************",
        "nUserInfo": "%7B%22uid%22%3A17660150%2C%22username%22%3A%22bu4mcw%22%2C%22nickname%22%3A%22slbj%22%2C%22headimg%22%3A%22%2F%2Fwx.qlogo.cn%2Fmmhead%2FQ3auHgzwzM6ZGbu2ktYpF8guRGZyeOwib8rfwYpDrfriaGHq6C0JGILA%2F0%22%2C%22identity%22%3A3%2C%22package_id%22%3A172%2C%22user_type%22%3A1%7D",
        "login_sign": "uFRW",
        "Hm_lvt_fd7bb595aa1a63da4ac8d6dc559190f8": "**********",
        "Qs_pv_449068": "1599554740656175900%2C2420804127808235000%2C3903064451979088000%2C3731867996238085000%2C3544803377954990000",
        "Hm_lpvt_1e531798c221147143a6d33847c60bc5": "**********",
        "li_referrer": "https%3A%2F%2Fwww.acc5.com%2F",
        "web_auth": "bcb7cVdTri7EGjcIvKGtXMcdEf1H3B1bL%2Fe3JlwNIhsErYz%2F8D361ibI",
        "li_api_auth": "f2cbMfnzB%2B%2Fq57%2F3RnxN8Ktg2L4ZBp6bS8gR2uRmVFNcI%2F8uv8%2BJqa7zO9SPssiG%2BA",
        "li_play_video": "AABhY2M1X3ZpZGVvLmtleQ%3D%3D",
        "li_learn_time": "91",
        "Hm_lpvt_fd7bb595aa1a63da4ac8d6dc559190f8": "1755852580",
        "XSRF-TOKEN": "eyJpdiI6Im1vRUQ3Q0pvSU9zNnlEbGh0OVEzRXc9PSIsInZhbHVlIjoibGhoMjE5MW9SQXRGN1VMSTl4M0VoVUNcL3kwQ0hINzdZb0RpV2h1T1wvclVQeEt4XC9kXC9naGVielcxZ2hzekRnUTc2TlF2cFVrZk1nK0JUMTFTek5EcGR3PT0iLCJtYWMiOiJiMzQ3YjJmN2QxNTUzNzM5NmIyMzliNzA0ZjJkM2JiYTYxODE2ZjY4OGJhNmQ3MDZlZDNiNTdiMjI1YjI3NDllIn0%3D",
        "laravel_session": "eyJpdiI6ImQzQjY0VHNZbVdmM1gzZFpVU2RuZEE9PSIsInZhbHVlIjoiK2ltQ0VSaVhmcU1EM2NZUEE0VzM2NVE0M0FUM0JMQ1JJRUp4a0pkSkFMNHhPQlpkenZHS3RlNnpLSnBVK0NUREE4THFoVTVpZm5WZW9wd29zTlJERXc9PSIsIm1hYyI6IjU3OWZkNWY5YTU0NzI5NzA2NzdhODkwOWY3N2Y5ZmE0NzFjMTUzMjM1ZDZkYzczODRlYWIyYThkY2IzMjIwNjUifQ%3D%3D",
        "remember_web_42ce96a7ef5fe5bfb331eff1c0bae54a792cf090": "eyJpdiI6IktNRnhGZitsVW9rRTZLaFRob0hZNkE9PSIsInZhbHVlIjoiM3VUcCtIRFd6MUNZNlVPQzRZWVhxaHByN0NHbmY3dTFmbzFzRUxkZTNxXC95VDBaWTNobGVCb0pQZ2l1VEVnVTRreW5yU0I2SU1XNjlzRitDQ1RzZ21cL2N4SDNhVSsrVGtCbjhadXBLSUlydz0iLCJtYWMiOiJmZTkzYTdlNTc4ODZjZWU1YTA2OGUyYzQwZDU0MTE5YjI4NTI0YzU2ZTEwMTU5ODQxMDIyYjEwYzAwOTFiYzE2In0%3D"
    }

    # 先访问个人中心
    session.get(f"{base_url}/home/", headers=headers, cookies=cookies)
    time.sleep(1)

    # 获取课程页面
    response = session.get(url, headers=headers, cookies=cookies)
    html = response.text

    # 解析课程
    soup = BeautifulSoup(html, 'html.parser')
    courses = []

    for element in soup.find_all('div', class_='play-list'):
        # 课程ID
        checkbox = element.find('input', class_='delete-play')
        course_id = checkbox.get('value', '') if checkbox else ''

        # 课程标题
        title_span = element.find('span')
        title = title_span.get_text(strip=True) if title_span else ''

        # 主讲老师
        teacher_elem = element.find(class_='teacher-is')
        teacher = teacher_elem.get_text(strip=True).replace('主讲：', '') if teacher_elem else ''

        # 学习进度
        progress_elem = element.find(class_='seen-mask')
        progress = '0%'
        if progress_elem:
            progress_p = progress_elem.find('p')
            if progress_p:
                progress_text = progress_p.get_text(strip=True)
                match = re.search(r'已学完(\d+)%', progress_text)
                if match:
                    progress = match.group(1) + '%'

        if course_id and title:
            courses.append({
                'id': course_id,
                'title': title,
                'teacher': teacher,
                'progress': progress
            })

    return courses

def print_course_details(course_info):
    """打印课程详情"""
    if not course_info:
        print("❌ 获取课程详情失败")
        return

    print(f"\n📖 课程详情: {course_info['title']}")
    print("=" * 60)
    print(f"👨‍🏫 主讲老师: {course_info['teacher']}")
    print(f"📊 课程信息: {course_info['total_lessons']} | {course_info['students']}")

    if course_info['description']:
        print(f"📝 课程简介: {course_info['description']}")

    print(f"\n📋 章节列表 ({len(course_info['lessons'])} 个章节):")
    print("-" * 60)

    for lesson in course_info['lessons']:
        trial_mark = " 🎯" if lesson['is_trial'] else ""
        print(f"{lesson['index']:2d}. {lesson['title']}{trial_mark}")
        print(f"   ⏱️  时长: {lesson['duration']}")

    print()

def print_courses(courses):
    """打印课程列表"""
    print(f"\n📚 会计学堂课程列表 ({len(courses)} 个课程)\n")

    for i, course in enumerate(courses, 1):
        print(f"{i}. [{course['id']}] {course['title']}")
        print(f"   👨‍🏫 主讲: {course['teacher']}")
        print(f"   📊 进度: {course['progress']}")
        print()

def get_unifyplay_id_from_page(course_id, lesson_id):
    """从课程页面提取unifyplay ID"""
    session = requests.Session()

    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }

    # 建立会话
    session.get("https://m.acc5.com/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)

    # 访问课程页面
    lesson_url = f"https://m.acc5.com/course/course_{course_id}/learn/lesson_{lesson_id}/"
    response = session.get(lesson_url, headers=headers, cookies=COOKIES)

    if response.status_code != 200:
        print(f"❌ 无法访问课程页面: {response.status_code}")
        return None

    # 查找unifyplay链接
    import re
    unifyplay_pattern = r'https://v\.acc5\.com/unifyplay_(\d+)'
    matches = re.findall(unifyplay_pattern, response.text)

    if matches:
        unifyplay_id = matches[0]
        print(f"✅ 找到unifyplay ID: {unifyplay_id}")
        return unifyplay_id
    else:
        print("❌ 未找到unifyplay ID")
        return None

def get_unifyplay_id_from_page(course_id, lesson_id):
    """从课程页面提取unifyplay ID"""
    session = requests.Session()

    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }

    # 建立会话
    session.get("https://m.acc5.com/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)

    # 访问课程页面
    lesson_url = f"https://m.acc5.com/course/course_{course_id}/learn/lesson_{lesson_id}/"
    response = session.get(lesson_url, headers=headers, cookies=COOKIES)

    if response.status_code != 200:
        print(f"❌ 无法访问课程页面: {response.status_code}")
        return None

    # 查找unifyplay链接
    import re
    unifyplay_pattern = r'https://v\.acc5\.com/unifyplay_(\d+)'
    matches = re.findall(unifyplay_pattern, response.text)

    if matches:
        unifyplay_id = matches[0]
        print(f"✅ 找到unifyplay ID: {unifyplay_id}")
        return unifyplay_id
    else:
        print("❌ 未找到unifyplay ID，可能不是视频文件")
        return None

def get_video_url(lesson_id, course_id="15361"):
    """获取视频下载链接 - 新的正确方法"""
    print(f"🔍 获取视频链接: lesson_id={lesson_id}, course_id={course_id}")

    # 第一步：从页面获取unifyplay ID
    unifyplay_id = get_unifyplay_id_from_page(course_id, lesson_id)
    if not unifyplay_id:
        return None

    # 第二步：请求unifyplay URL获取重定向的m3u8链接
    session = requests.Session()

    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "priority": "i",
        "range": "bytes=0-",
        "referer": "https://m.acc5.com/",
        "sec-ch-ua": '"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "video",
        "sec-fetch-mode": "no-cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }

    unifyplay_url = f"https://v.acc5.com/unifyplay_{unifyplay_id}"
    print(f"🔄 请求unifyplay URL: {unifyplay_url}")

    try:
        # 不跟随重定向，获取重定向的目标URL
        response = session.get(unifyplay_url, headers=headers, cookies=COOKIES, allow_redirects=False)
        print(f"📊 响应状态: {response.status_code}")

        if response.status_code in [301, 302, 303, 307, 308]:
            # 获取重定向的目标URL
            redirect_url = response.headers.get('Location')
            if redirect_url:
                print(f"✅ 获取到重定向URL: {redirect_url}")

                # 检查是否是m3u8链接
                if '.m3u8' in redirect_url:
                    print("🎉 找到m3u8链接!")
                    return redirect_url
                else:
                    print("⚠️ 重定向URL不是m3u8格式")
                    return redirect_url
            else:
                print("❌ 重定向响应中没有Location头")
        else:
            print(f"❌ 意外的响应状态: {response.status_code}")
            print(f"📄 响应内容: {response.text[:200]}...")

    except Exception as e:
        print(f"❌ 请求unifyplay URL失败: {e}")

    return None

def download_video(encrypted_url, filename):
    """下载视频文件"""
    try:
        print(f"🔄 正在解析视频链接: {encrypted_url[:50]}...")

        # 尝试多种解密方式
        video_url = None

        # 方法1: 尝试Base64解码
        import base64
        try:
            decoded = base64.b64decode(encrypted_url)
            video_url = decoded.decode('utf-8')
            if video_url.startswith('http'):
                print("✅ Base64解码成功")
                print(f"📺 视频链接: {video_url}")
            else:
                video_url = None
        except:
            pass

        # 方法2: 使用JavaScript的authcode解密算法
        if not video_url:
            print("🔍 尝试JavaScript authcode解密...")
            decrypted_result = authcode_decrypt(encrypted_url)
            if decrypted_result:
                print("✅ authcode解密成功")
                print(f"📋 解密结果: {decrypted_result}")

                # 尝试解析JSON
                try:
                    import json
                    json_data = json.loads(decrypted_result)
                    print("🎯 JSON解析成功:")
                    print(json.dumps(json_data, indent=2, ensure_ascii=False))

                    # 检查JSON中是否包含视频链接
                    if 'data' in json_data and isinstance(json_data['data'], dict):
                        data = json_data['data']
                        # 查找可能的视频链接字段
                        for key in ['url', 'video_url', 'play_url', 'stream_url', 'src', 'link']:
                            if key in data and isinstance(data[key], str) and data[key].startswith('http'):
                                video_url = data[key]
                                print(f"🎬 在JSON中找到视频链接: {video_url}")
                                break

                        # 如果没找到直接的链接，打印所有字段
                        if not video_url:
                            print("🔍 JSON数据中的所有字段:")
                            for key, value in data.items():
                                print(f"  {key}: {value}")

                    if not video_url and decrypted_result.startswith('http'):
                        video_url = decrypted_result
                        print(f"📺 视频链接: {video_url}")

                except json.JSONDecodeError:
                    # 如果不是JSON，检查是否是直接的URL
                    if decrypted_result.startswith('http'):
                        video_url = decrypted_result
                        print(f"📺 视频链接: {video_url}")
                    else:
                        print("⚠️ 解密结果不是JSON也不是URL")
                        print(f"📋 原始解密结果: {decrypted_result[:200]}...")
            else:
                print("❌ authcode解密失败")

        # 方法3: 尝试URL解码
        if not video_url:
            try:
                import urllib.parse
                potential_url = urllib.parse.unquote(encrypted_url)
                if potential_url.startswith('http'):
                    video_url = potential_url
                    print("✅ URL解码成功")
                    print(f"📺 视频链接: {video_url}")
            except:
                pass

        # 方法2: 如果Base64失败，尝试其他解密方式
        if not video_url:
            # 可能是一些自定义的加密算法
            # 这里可以添加更多解密逻辑
            print("🔍 尝试其他解密方法...")

            # 简单示例：如果包含特定模式，可能需要逆向处理
            if '/' in encrypted_url and '.' in encrypted_url:
                print("⚠️  检测到可能的URL模式，跳过解密")
                video_url = encrypted_url  # 直接使用

        if not video_url:
            print("❌ 无法解析视频链接")
            print(f"🔐 原始加密字符串: {encrypted_url}")
            return False

        # 如果解析成功，下载视频
        if video_url.startswith('http'):
            print(f"📥 开始下载: {filename}")

            # 创建下载目录
            import os
            os.makedirs('downloads', exist_ok=True)
            filepath = os.path.join('downloads', filename)

            response = requests.get(video_url, stream=True, timeout=30)

            if response.status_code == 200:
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0

                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)

                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                print(".1f", end='', flush=True)

                print(f"\n✅ 下载完成: {filepath}")
                print(f"📊 文件大小: {downloaded} bytes")
                return True
            else:
                print(f"❌ 下载失败: HTTP {response.status_code}")
                print(f"📝 响应内容: {response.text[:200]}...")
                return False
        else:
            print("❌ 无效的视频链接格式")
            return False

    except Exception as e:
        print(f"❌ 下载出错: {e}")
        import traceback
        print(f"📋 详细错误: {traceback.format_exc()}")
        return False

def select_and_show_course_details(courses):
    """让用户选择课程并显示详情"""
    while True:
        try:
            choice = input("\n请选择课程序号 (1-20) 或按回车退出: ").strip()

            if not choice:
                print("👋 再见！")
                break

            index = int(choice) - 1
            if 0 <= index < len(courses):
                selected_course = courses[index]
                course_url = f"https://m.acc5.com/course/course_{selected_course['id']}/learn/lesson_1"

                print(f"\n🔄 正在获取课程详情: {selected_course['title']}")
                course_info = get_course_details(course_url)

                if course_info:
                    print_course_details(course_info)

                    # 询问是否下载视频
                    download_choice = input("\n💡 是否下载视频？输入章节序号下载 (1-7) 或回车跳过: ").strip()

                    if download_choice and download_choice.isdigit():
                        lesson_index = int(download_choice) - 1
                        if 0 <= lesson_index < len(course_info['lessons']):
                            lesson = course_info['lessons'][lesson_index]
                            lesson_id = f"9460{lesson['index']:02d}"  # 构造lesson_id

                            print(f"🎬 准备下载: {lesson['title']}")

                            # 获取视频链接
                            encrypted_url = get_video_url(lesson_id, selected_course['id'])
                            if encrypted_url:
                                filename = f"{selected_course['id']}_{lesson['title']}.mp4"
                                download_video(encrypted_url, filename)
                            else:
                                print("❌ 获取视频链接失败")
                        else:
                            print("❌ 无效的章节序号")
                else:
                    print("❌ 获取课程详情失败")
            else:
                print(f"❌ 无效序号，请输入 1-{len(courses)} 之间的数字")

        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # 如果提供了URL参数，直接获取课程详情
        course_url = sys.argv[1]
        print(f"🔄 正在获取课程详情: {course_url}")
        course_info = get_course_details(course_url)
        print_course_details(course_info)
    else:
        # 默认流程：显示课程列表 -> 用户选择 -> 显示详情
        print("🔄 正在获取课程列表...")
        courses = get_courses()

        if courses:
            print_courses(courses)
            select_and_show_course_details(courses)
        else:
            print("❌ 获取课程列表失败")
