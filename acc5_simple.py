#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会计学堂课程列表获取器 - 简化版
"""

import requests
from bs4 import BeautifulSoup
import time
import re
import hashlib
from config import COOKIES

def md5(string):
    """MD5哈希函数"""
    return hashlib.md5(string.encode('utf-8')).hexdigest()

def authcode_decrypt(str_data, key='acc5app2015SHdEreIoB6wjpiOZ'):
    """Python版本的authcode解密函数，模拟JavaScript版本"""
    try:
        operation = 'DECODE'
        ckey_length = 4

        # 生成密钥
        key = md5(key)
        keya = md5(key[:16])
        keyb = md5(key[16:32])

        # 提取密钥c
        if len(str_data) < ckey_length:
            return None
        keyc = str_data[:ckey_length]
        str_data = str_data[ckey_length:]

        # Base64解码
        import base64
        try:
            # 确保Base64字符串长度是4的倍数
            padding = 4 - len(str_data) % 4
            if padding != 4:
                str_data += '=' * padding

            decoded_bytes = base64.b64decode(str_data)
            str_data = decoded_bytes.decode('utf-8', errors='ignore')
        except Exception as e:
            print(f"Base64解码失败: {e}")
            return None

        # 生成cryptkey
        cryptkey = keya + md5(keya + keyc)

        # RC4解密
        box = list(range(256))
        rndkey = [ord(cryptkey[i % len(cryptkey)]) for i in range(256)]

        # 初始化密钥流
        j = 0
        for i in range(256):
            j = (j + box[i] + rndkey[i]) % 256
            box[i], box[j] = box[j], box[i]

        # 解密数据
        a = j = 0
        result = []
        for char in str_data:
            a = (a + 1) % 256
            j = (j + box[a]) % 256
            box[a], box[j] = box[j], box[a]
            k = box[(box[a] + box[j]) % 256]
            result.append(chr(ord(char) ^ k))

        result = ''.join(result)

        # 验证并返回结果
        if len(result) > 26:
            # 检查时间戳验证
            timestamp_part = result[:10]
            hash_part = result[10:26]
            data_part = result[26:]

            expected_hash = md5(data_part + keyb)[:16]

            if hash_part == expected_hash:
                # 验证时间戳（如果需要的话）
                try:
                    timestamp = int(timestamp_part)
                    current_time = int(time.time())
                    # 如果时间戳验证失败，也返回数据（可能服务器时间不同步）
                    return data_part
                except:
                    return data_part
            else:
                print(f"哈希验证失败: 期望 {expected_hash}, 实际 {hash_part}")
                # 即使验证失败也返回结果，可能算法有细微差别
                return data_part

        return result

    except Exception as e:
        print(f"authcode解密错误: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

def get_course_details(course_url):
    """获取课程详情"""
    session = requests.Session()
    base_url = "https://m.acc5.com"

    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "referer": "https://m.acc5.com/home/<USER>",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
    }

    # 先访问个人中心建立会话
    session.get(f"{base_url}/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)

    # 获取课程详情页面
    response = session.get(course_url, headers=headers, cookies=COOKIES)

    if response.status_code != 200:
        print(f"❌ 请求失败: {response.status_code}")
        return None

    html = response.text
    soup = BeautifulSoup(html, 'html.parser')

    # 提取课程基本信息
    course_info = {}

    # 课程标题
    title_elem = soup.find('span', class_='left')
    course_info['title'] = title_elem.get_text(strip=True) if title_elem else '未知课程'

    # 课程统计信息
    count_elem = soup.find('div', class_='msg-is count')
    course_info['total_lessons'] = count_elem.get_text(strip=True) if count_elem else '未知'

    num_elem = soup.find('div', class_='msg-is num')
    course_info['students'] = num_elem.get_text(strip=True) if num_elem else '未知'

    # 课程简介
    intro_elem = soup.find('div', class_='detail-intro')
    course_info['description'] = intro_elem.find('p').get_text(strip=True) if intro_elem else ''

    # 教师信息
    teacher_elem = soup.find('div', class_='teacher-is')
    course_info['teacher'] = teacher_elem.find('span', class_='name').get_text(strip=True) if teacher_elem else '未知'

    # 提取章节列表
    lessons = []
    lesson_elements = soup.find_all('li', class_='son-title')

    for i, lesson in enumerate(lesson_elements, 1):
        # 章节标题
        title_elem = lesson.find('p', class_='title')
        title = title_elem.get_text(strip=True) if title_elem else f'第{i}节'

        # 时长信息
        time_elem = lesson.find('p', class_='time')
        duration = time_elem.get_text(strip=True) if time_elem else '00:00'

        # 是否可试看
        is_trial = '试看' in lesson.get_text()

        # 播放状态
        if 'in-play-icon' in str(lesson):
            status = '🔄 正在播放'
        elif 'play-icon' in str(lesson):
            status = '⏸️ 未开始'
        else:
            status = '✅ 已完成'

        lessons.append({
            'index': i,
            'title': title,
            'duration': duration,
            'is_trial': is_trial,
            'status': status
        })

    course_info['lessons'] = lessons
    return course_info

def get_courses():
    """获取课程列表"""
    session = requests.Session()
    base_url = "https://m.acc5.com"
    url = f"{base_url}/home/<USER>"

    # 请求头
    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "referer": "https://m.acc5.com/home/",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
    }

    # Cookies
    cookies = {
        "Qs_lvt_449068": "**********",
        "li_first_access_time": "**********",
        "li_last_login_time": "**********",
        "Hm_lvt_1e531798c221147143a6d33847c60bc5": "**********,**********,**********,**********",
        "HMACCOUNT": "****************",
        "nUserInfo": "%7B%22uid%22%3A17660150%2C%22username%22%3A%22bu4mcw%22%2C%22nickname%22%3A%22slbj%22%2C%22headimg%22%3A%22%2F%2Fwx.qlogo.cn%2Fmmhead%2FQ3auHgzwzM6ZGbu2ktYpF8guRGZyeOwib8rfwYpDrfriaGHq6C0JGILA%2F0%22%2C%22identity%22%3A3%2C%22package_id%22%3A172%2C%22user_type%22%3A1%7D",
        "login_sign": "uFRW",
        "Hm_lvt_fd7bb595aa1a63da4ac8d6dc559190f8": "**********",
        "Qs_pv_449068": "1599554740656175900%2C2420804127808235000%2C3903064451979088000%2C3731867996238085000%2C3544803377954990000",
        "Hm_lpvt_1e531798c221147143a6d33847c60bc5": "**********",
        "li_referrer": "https%3A%2F%2Fwww.acc5.com%2F",
        "web_auth": "bcb7cVdTri7EGjcIvKGtXMcdEf1H3B1bL%2Fe3JlwNIhsErYz%2F8D361ibI",
        "li_api_auth": "f2cbMfnzB%2B%2Fq57%2F3RnxN8Ktg2L4ZBp6bS8gR2uRmVFNcI%2F8uv8%2BJqa7zO9SPssiG%2BA",
        "li_play_video": "AABhY2M1X3ZpZGVvLmtleQ%3D%3D",
        "li_learn_time": "91",
        "Hm_lpvt_fd7bb595aa1a63da4ac8d6dc559190f8": "1755852580",
        "XSRF-TOKEN": "eyJpdiI6Im1vRUQ3Q0pvSU9zNnlEbGh0OVEzRXc9PSIsInZhbHVlIjoibGhoMjE5MW9SQXRGN1VMSTl4M0VoVUNcL3kwQ0hINzdZb0RpV2h1T1wvclVQeEt4XC9kXC9naGVielcxZ2hzekRnUTc2TlF2cFVrZk1nK0JUMTFTek5EcGR3PT0iLCJtYWMiOiJiMzQ3YjJmN2QxNTUzNzM5NmIyMzliNzA0ZjJkM2JiYTYxODE2ZjY4OGJhNmQ3MDZlZDNiNTdiMjI1YjI3NDllIn0%3D",
        "laravel_session": "eyJpdiI6ImQzQjY0VHNZbVdmM1gzZFpVU2RuZEE9PSIsInZhbHVlIjoiK2ltQ0VSaVhmcU1EM2NZUEE0VzM2NVE0M0FUM0JMQ1JJRUp4a0pkSkFMNHhPQlpkenZHS3RlNnpLSnBVK0NUREE4THFoVTVpZm5WZW9wd29zTlJERXc9PSIsIm1hYyI6IjU3OWZkNWY5YTU0NzI5NzA2NzdhODkwOWY3N2Y5ZmE0NzFjMTUzMjM1ZDZkYzczODRlYWIyYThkY2IzMjIwNjUifQ%3D%3D",
        "remember_web_42ce96a7ef5fe5bfb331eff1c0bae54a792cf090": "eyJpdiI6IktNRnhGZitsVW9rRTZLaFRob0hZNkE9PSIsInZhbHVlIjoiM3VUcCtIRFd6MUNZNlVPQzRZWVhxaHByN0NHbmY3dTFmbzFzRUxkZTNxXC95VDBaWTNobGVCb0pQZ2l1VEVnVTRreW5yU0I2SU1XNjlzRitDQ1RzZ21cL2N4SDNhVSsrVGtCbjhadXBLSUlydz0iLCJtYWMiOiJmZTkzYTdlNTc4ODZjZWU1YTA2OGUyYzQwZDU0MTE5YjI4NTI0YzU2ZTEwMTU5ODQxMDIyYjEwYzAwOTFiYzE2In0%3D"
    }

    # 先访问个人中心
    session.get(f"{base_url}/home/", headers=headers, cookies=cookies)
    time.sleep(1)

    # 获取课程页面
    response = session.get(url, headers=headers, cookies=cookies)
    html = response.text

    # 解析课程
    soup = BeautifulSoup(html, 'html.parser')
    courses = []

    for element in soup.find_all('div', class_='play-list'):
        # 课程ID
        checkbox = element.find('input', class_='delete-play')
        course_id = checkbox.get('value', '') if checkbox else ''

        # 课程标题
        title_span = element.find('span')
        title = title_span.get_text(strip=True) if title_span else ''

        # 主讲老师
        teacher_elem = element.find(class_='teacher-is')
        teacher = teacher_elem.get_text(strip=True).replace('主讲：', '') if teacher_elem else ''

        # 学习进度
        progress_elem = element.find(class_='seen-mask')
        progress = '0%'
        if progress_elem:
            progress_p = progress_elem.find('p')
            if progress_p:
                progress_text = progress_p.get_text(strip=True)
                match = re.search(r'已学完(\d+)%', progress_text)
                if match:
                    progress = match.group(1) + '%'

        if course_id and title:
            courses.append({
                'id': course_id,
                'title': title,
                'teacher': teacher,
                'progress': progress
            })

    return courses

def print_course_details(course_info):
    """打印课程详情"""
    if not course_info:
        print("❌ 获取课程详情失败")
        return

    print(f"\n📖 课程详情: {course_info['title']}")
    print("=" * 60)
    print(f"👨‍🏫 主讲老师: {course_info['teacher']}")
    print(f"📊 课程信息: {course_info['total_lessons']} | {course_info['students']}")

    if course_info['description']:
        print(f"📝 课程简介: {course_info['description']}")

    print(f"\n📋 章节列表 ({len(course_info['lessons'])} 个章节):")
    print("-" * 60)

    for lesson in course_info['lessons']:
        trial_mark = " 🎯" if lesson['is_trial'] else ""
        print(f"{lesson['index']:2d}. {lesson['title']}{trial_mark}")
        print(f"   ⏱️  时长: {lesson['duration']}")

    print()

def print_courses(courses):
    """打印课程列表"""
    print(f"\n📚 会计学堂课程列表 ({len(courses)} 个课程)\n")

    for i, course in enumerate(courses, 1):
        print(f"{i}. [{course['id']}] {course['title']}")
        print(f"   👨‍🏫 主讲: {course['teacher']}")
        print(f"   📊 进度: {course['progress']}")
        print()

def get_video_url(lesson_id):
    """获取视频下载链接"""
    session = requests.Session()
    base_url = "https://m.acc5.com"
    api_url = f"{base_url}/api/v3/course/get_comment?lesson_id={lesson_id}"

    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "referer": f"https://m.acc5.com/course/course_15361/learn/lesson_{lesson_id}/",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
        "x-requested-with": "XMLHttpRequest",
        "priority": "u=1, i"
    }

    # 先访问个人中心建立会话
    session.get(f"{base_url}/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)

    # 获取视频链接
    response = session.get(api_url, headers=headers, cookies=COOKIES)

    if response.status_code == 200:
        try:
            data = response.json()
            if data.get('retcode') == 200 and 'result' in data:
                return data['result']
        except:
            pass

    return None

def download_video(encrypted_url, filename):
    """下载视频文件"""
    try:
        print(f"🔄 正在解析视频链接: {encrypted_url[:50]}...")

        # 尝试多种解密方式
        video_url = None

        # 方法1: 尝试Base64解码
        import base64
        try:
            decoded = base64.b64decode(encrypted_url)
            video_url = decoded.decode('utf-8')
            if video_url.startswith('http'):
                print("✅ Base64解码成功")
                print(f"📺 视频链接: {video_url}")
            else:
                video_url = None
        except:
            pass

        # 方法2: 使用JavaScript的authcode解密算法
        if not video_url:
            print("🔍 尝试JavaScript authcode解密...")
            video_url = authcode_decrypt(encrypted_url)
            if video_url and video_url.startswith('http'):
                print("✅ authcode解密成功")
                print(f"📺 视频链接: {video_url}")

        # 方法3: 尝试URL解码
        if not video_url:
            try:
                import urllib.parse
                potential_url = urllib.parse.unquote(encrypted_url)
                if potential_url.startswith('http'):
                    video_url = potential_url
                    print("✅ URL解码成功")
                    print(f"📺 视频链接: {video_url}")
            except:
                pass

        # 方法2: 如果Base64失败，尝试其他解密方式
        if not video_url:
            # 可能是一些自定义的加密算法
            # 这里可以添加更多解密逻辑
            print("🔍 尝试其他解密方法...")

            # 简单示例：如果包含特定模式，可能需要逆向处理
            if '/' in encrypted_url and '.' in encrypted_url:
                print("⚠️  检测到可能的URL模式，跳过解密")
                video_url = encrypted_url  # 直接使用

        if not video_url:
            print("❌ 无法解析视频链接")
            print(f"🔐 原始加密字符串: {encrypted_url}")
            return False

        # 如果解析成功，下载视频
        if video_url.startswith('http'):
            print(f"📥 开始下载: {filename}")

            # 创建下载目录
            import os
            os.makedirs('downloads', exist_ok=True)
            filepath = os.path.join('downloads', filename)

            response = requests.get(video_url, stream=True, timeout=30)

            if response.status_code == 200:
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0

                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)

                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                print(".1f", end='', flush=True)

                print(f"\n✅ 下载完成: {filepath}")
                print(f"📊 文件大小: {downloaded} bytes")
                return True
            else:
                print(f"❌ 下载失败: HTTP {response.status_code}")
                print(f"📝 响应内容: {response.text[:200]}...")
                return False
        else:
            print("❌ 无效的视频链接格式")
            return False

    except Exception as e:
        print(f"❌ 下载出错: {e}")
        import traceback
        print(f"📋 详细错误: {traceback.format_exc()}")
        return False

def select_and_show_course_details(courses):
    """让用户选择课程并显示详情"""
    while True:
        try:
            choice = input("\n请选择课程序号 (1-20) 或按回车退出: ").strip()

            if not choice:
                print("👋 再见！")
                break

            index = int(choice) - 1
            if 0 <= index < len(courses):
                selected_course = courses[index]
                course_url = f"https://m.acc5.com/course/course_{selected_course['id']}/learn/lesson_1"

                print(f"\n🔄 正在获取课程详情: {selected_course['title']}")
                course_info = get_course_details(course_url)

                if course_info:
                    print_course_details(course_info)

                    # 询问是否下载视频
                    download_choice = input("\n💡 是否下载视频？输入章节序号下载 (1-7) 或回车跳过: ").strip()

                    if download_choice and download_choice.isdigit():
                        lesson_index = int(download_choice) - 1
                        if 0 <= lesson_index < len(course_info['lessons']):
                            lesson = course_info['lessons'][lesson_index]
                            lesson_id = f"9460{lesson['index']:02d}"  # 构造lesson_id

                            print(f"🎬 准备下载: {lesson['title']}")

                            # 获取视频链接
                            encrypted_url = get_video_url(lesson_id)
                            if encrypted_url:
                                filename = f"{selected_course['id']}_{lesson['title']}.mp4"
                                download_video(encrypted_url, filename)
                            else:
                                print("❌ 获取视频链接失败")
                        else:
                            print("❌ 无效的章节序号")
                else:
                    print("❌ 获取课程详情失败")
            else:
                print(f"❌ 无效序号，请输入 1-{len(courses)} 之间的数字")

        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # 如果提供了URL参数，直接获取课程详情
        course_url = sys.argv[1]
        print(f"🔄 正在获取课程详情: {course_url}")
        course_info = get_course_details(course_url)
        print_course_details(course_info)
    else:
        # 默认流程：显示课程列表 -> 用户选择 -> 显示详情
        print("🔄 正在获取课程列表...")
        courses = get_courses()

        if courses:
            print_courses(courses)
            select_and_show_course_details(courses)
        else:
            print("❌ 获取课程列表失败")
