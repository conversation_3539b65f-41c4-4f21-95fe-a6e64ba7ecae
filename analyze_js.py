#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析JavaScript解密逻辑
"""

import requests
from bs4 import BeautifulSoup
import re

def download_js_files():
    """下载相关的JavaScript文件"""
    base_url = "https://static.acc5.com/static_macc5/js"

    js_files = [
        "security.js",  # 安全相关，可能包含解密逻辑
        "api.js",       # API相关
        "OFunc.js"      # 工具函数
    ]

    for js_file in js_files:
        try:
            url = f"{base_url}/{js_file}"
            print(f"📥 下载: {url}")

            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                with open(f"js_{js_file}", 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"✅ 保存到: js_{js_file}")
            else:
                print(f"❌ 下载失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 错误: {e}")

def analyze_decode_function():
    """分析decodeResult函数"""
    try:
        with open('js_security.js', 'r', encoding='utf-8') as f:
            content = f.read()

        # 查找decodeResult函数
        decode_pattern = r'decodeResult\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]*\}'
        matches = re.findall(decode_pattern, content, re.DOTALL)

        if matches:
            print("🎯 找到decodeResult函数:")
            for match in matches:
                print(match)
                print("-" * 50)
        else:
            print("❌ 未找到decodeResult函数")

        # 查找其他可能的解密函数
        encrypt_patterns = [
            r'function.*decrypt.*\{[^}]*\}',
            r'function.*decode.*\{[^}]*\}',
            r'function.*parse.*\{[^}]*\}'
        ]

        for pattern in encrypt_patterns:
            matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
            if matches:
                print(f"🔍 找到可能的解密函数 ({pattern}):")
                for match in matches[:3]:  # 只显示前3个
                    print(match[:200] + "..." if len(match) > 200 else match)
                    print("-" * 50)

    except FileNotFoundError:
        print("❌ 请先运行 download_js_files() 下载JS文件")
    except Exception as e:
        print(f"❌ 分析错误: {e}")

def test_decrypt_methods():
    """测试各种解密方法"""
    # 测试字符串
    encrypted = "52fbxHHBtv4BqpiyltlXvYNSwmCqempFyKrJm05iMW8Vr+r5SQdm7MNmlebYZJ4l6zTGxQyCxWQ1wwSf4irfUi7c0mE1eRts5t891CunB0RF0de8YJhj005yTsZYIO5ooiWIHojSEFqRv1QKq+0htpzE/98b+IBBKG2zZbhoPmocInZ5/GnE9JRv9gVHjBDVdUWH0bF8YyI"

    print(f"🔐 测试解密: {encrypted[:50]}...")

    # 方法1: Base64
    try:
        import base64
        decoded = base64.b64decode(encrypted)
        result = decoded.decode('utf-8')
        print(f"✅ Base64解码: {result[:100]}...")
        if result.startswith('http'):
            print("🎉 这是一个有效的URL!")
    except:
        print("❌ Base64解码失败")

    # 方法2: URL解码
    try:
        import urllib.parse
        result = urllib.parse.unquote(encrypted)
        print(f"✅ URL解码: {result[:100]}...")
        if result.startswith('http'):
            print("🎉 这是一个有效的URL!")
    except:
        print("❌ URL解码失败")

    # 方法3: 检查是否是某种移位加密
    for shift in range(1, 26):
        try:
            result = ''.join(chr((ord(c) - shift) % 256) if c.isalnum() else c for c in encrypted[:20])
            print(f"移位{shift}: {result}")
        except:
            pass

if __name__ == "__main__":
    print("🔍 JavaScript解密逻辑分析")
    print("=" * 50)

    # 下载JS文件
    print("\n📥 下载JavaScript文件...")
    download_js_files()

    # 分析解密函数
    print("\n🎯 分析解密函数...")
    analyze_decode_function()

    # 测试解密方法
    print("\n🧪 测试解密方法...")
    test_decrypt_methods()

    print("\n" + "=" * 50)
    print("💡 建议:")
    print("1. 检查security.js文件中的decodeResult函数实现")
    print("2. 可能需要逆向工程JavaScript加密算法")
    print("3. 尝试使用浏览器开发者工具拦截真实请求")
    print("4. 查看是否有其他API端点提供未加密的视频链接")
