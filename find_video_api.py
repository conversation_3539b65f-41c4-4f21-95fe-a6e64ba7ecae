#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找正确的视频API - 通过模拟浏览器行为
"""

import requests
from config import COOKIES
import time
from acc5_simple import authcode_decrypt
import json

def test_video_apis():
    """测试各种可能的视频API"""
    session = requests.Session()
    
    # 课程和章节信息
    course_id = "15361"
    lesson_id = "94601"  # 第一个视频
    
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "referer": f"https://m.acc5.com/course/course_{course_id}/learn/lesson_{lesson_id}/",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
        "x-requested-with": "XMLHttpRequest",
    }
    
    # 建立会话
    print("🔄 建立会话...")
    session.get("https://m.acc5.com/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)
    
    # 访问课程页面
    print("🔄 访问课程页面...")
    course_url = f"https://m.acc5.com/course/course_{course_id}/"
    session.get(course_url, headers=headers, cookies=COOKIES)
    time.sleep(1)
    
    # 访问具体的课程学习页面
    print("🔄 访问课程学习页面...")
    lesson_url = f"https://m.acc5.com/course/course_{course_id}/learn/lesson_{lesson_id}/"
    response = session.get(lesson_url, headers=headers, cookies=COOKIES)
    print(f"📊 课程页面状态: {response.status_code}")
    
    # 分析页面内容，寻找可能的API调用
    if response.status_code == 200:
        page_content = response.text
        print("🔍 分析页面内容...")
        
        # 查找可能的API端点
        import re
        api_patterns = [
            r'/api/[^"\']*video[^"\']*',
            r'/api/[^"\']*lesson[^"\']*',
            r'/api/[^"\']*course[^"\']*',
            r'/module\.php\?[^"\']*video[^"\']*',
            r'get_comment[^"\']*',
            r'video_auth[^"\']*',
        ]
        
        found_apis = set()
        for pattern in api_patterns:
            matches = re.findall(pattern, page_content)
            for match in matches:
                found_apis.add(match)
        
        if found_apis:
            print("🎯 在页面中找到的API端点:")
            for api in sorted(found_apis):
                print(f"  - {api}")
        else:
            print("⚠️ 未在页面中找到明显的API端点")
    
    # 尝试一些常见的视频API模式
    video_api_candidates = [
        # 基于移动端的API
        f"https://m.acc5.com/api/v3/course/get_video?lesson_id={lesson_id}",
        f"https://m.acc5.com/api/v3/lesson/get_video?id={lesson_id}",
        f"https://m.acc5.com/api/v3/video/get?lesson_id={lesson_id}",
        f"https://m.acc5.com/api/v3/play/video?lesson_id={lesson_id}",
        
        # 基于主站的API
        f"https://www.acc5.com/api.php?c=app_video&lesson_id={lesson_id}",
        f"https://www.acc5.com/api.php?c=video&lesson_id={lesson_id}",
        f"https://www.acc5.com/api.php?c=course_video&lesson_id={lesson_id}",
        f"https://www.acc5.com/api.php?c=lesson_video&lesson_id={lesson_id}",
        
        # 带encrypt参数的API
        f"https://www.acc5.com/api.php?encrypt=1&c=app_video&lesson_id={lesson_id}",
        f"https://www.acc5.com/api.php?encrypt=1&c=video&lesson_id={lesson_id}",
        f"https://www.acc5.com/api.php?encrypt=1&c=course_video&lesson_id={lesson_id}",
        f"https://www.acc5.com/api.php?encrypt=1&c=lesson_video&lesson_id={lesson_id}",
        
        # 视频权限相关
        f"https://www.acc5.com/api.php?c=video_permission&lesson_id={lesson_id}",
        f"https://www.acc5.com/api.php?encrypt=1&c=video_permission&lesson_id={lesson_id}",
        
        # 播放相关
        f"https://m.acc5.com/api/v3/play?lesson_id={lesson_id}",
        f"https://m.acc5.com/api/v3/stream?lesson_id={lesson_id}",
        
        # 其他可能的模式
        f"https://m.acc5.com/course/course_{course_id}/api/video/{lesson_id}",
        f"https://m.acc5.com/lesson/{lesson_id}/video",
    ]
    
    print(f"\n🔍 测试 {len(video_api_candidates)} 个可能的视频API...")
    
    for i, api_url in enumerate(video_api_candidates, 1):
        print(f"\n[{i}/{len(video_api_candidates)}] 测试: {api_url}")
        
        try:
            response = session.get(api_url, headers=headers, cookies=COOKIES)
            print(f"  状态: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  JSON: {data}")
                    
                    # 检查是否有加密的result
                    if data.get('retcode') == 200 and 'result' in data:
                        encrypted_result = data['result']
                        print(f"  🔐 加密结果: {encrypted_result[:50]}...")
                        
                        # 尝试解密
                        decrypted = authcode_decrypt(encrypted_result)
                        if decrypted:
                            print(f"  🔓 解密结果: {decrypted}")
                            
                            # 检查是否包含视频链接
                            if 'http' in decrypted and ('mp4' in decrypted or 'm3u8' in decrypted or 'video' in decrypted):
                                print(f"  🎉 可能找到视频链接！")
                                return api_url, encrypted_result, decrypted
                        else:
                            print(f"  ❌ 解密失败")
                    
                except json.JSONDecodeError:
                    print(f"  📄 非JSON响应: {response.text[:100]}...")
            elif response.status_code == 404:
                print(f"  ❌ 404 - API不存在")
            else:
                print(f"  ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
        
        time.sleep(0.5)  # 避免请求过快
    
    print("\n❌ 未找到有效的视频API")
    return None, None, None

def test_different_lesson_ids():
    """测试不同的lesson_id"""
    print("\n🔍 测试不同的lesson_id...")
    
    # 尝试不同的lesson_id格式
    lesson_ids = [
        "94601",  # 原始ID
        "1",      # 简单序号
        "15361_1", # 课程ID_序号
        "946001", # 可能的变体
        "94600",  # 减1
        "94602",  # 加1
    ]
    
    session = requests.Session()
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
        "x-requested-with": "XMLHttpRequest",
    }
    
    session.get("https://m.acc5.com/home/", headers=headers, cookies=COOKIES)
    
    for lesson_id in lesson_ids:
        print(f"\n测试 lesson_id: {lesson_id}")
        api_url = f"https://m.acc5.com/api/v3/course/get_comment?lesson_id={lesson_id}"
        
        try:
            response = session.get(api_url, headers=headers, cookies=COOKIES)
            if response.status_code == 200:
                data = response.json()
                if data.get('retcode') == 200 and 'result' in data:
                    decrypted = authcode_decrypt(data['result'])
                    print(f"  解密结果: {decrypted}")
        except Exception as e:
            print(f"  错误: {e}")
        
        time.sleep(0.5)

if __name__ == "__main__":
    print("🎯 寻找正确的视频API")
    print("=" * 60)
    
    # 测试各种API
    api_url, encrypted_result, decrypted_result = test_video_apis()
    
    if api_url:
        print(f"\n🎉 找到有效的视频API!")
        print(f"API: {api_url}")
        print(f"加密结果: {encrypted_result}")
        print(f"解密结果: {decrypted_result}")
    else:
        # 如果没找到，尝试不同的lesson_id
        test_different_lesson_ids()
        
        print(f"\n💡 建议:")
        print("1. 使用浏览器开发者工具查看实际的网络请求")
        print("2. 检查是否需要特定的认证参数")
        print("3. 可能需要先调用其他API获取权限")
        print("4. 视频可能需要付费或特定权限才能访问")
