#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的API调用
"""

import requests
from config import COOKIES
import time
from acc5_simple import authcode_decrypt

def test_new_api_calls():
    """测试新的API调用"""
    session = requests.Session()
    base_url = "https://m.acc5.com"
    lesson_id = "94601"  # 第一个视频的ID
    course_id = "15361"
    
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "referer": f"https://m.acc5.com/course/course_{course_id}/learn/lesson_{lesson_id}/",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
        "x-requested-with": "XMLHttpRequest",
        "priority": "u=1, i"
    }

    # 先访问个人中心建立会话
    print("🔄 建立会话...")
    session.get(f"{base_url}/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)

    # 尝试多个可能的API端点
    api_endpoints = [
        # 原始的API
        f"{base_url}/api/v3/course/get_comment?lesson_id={lesson_id}",
        # 视频权限API
        f"https://www.acc5.com/api.php?c=app_video_permission&lesson_id={lesson_id}",
        # 视频密钥API
        f"https://www.acc5.com/module.php?c=video_auth&op=set&id={lesson_id}",
        # 课程详情API
        f"https://www.acc5.com/api.php?encrypt=1&c=app_course_view&has_chapter=1&id={course_id}",
        # 移动端课程API
        f"{base_url}/api/v3/course/lesson?lesson_id={lesson_id}",
        f"{base_url}/api/v3/course/video?lesson_id={lesson_id}",
        f"{base_url}/api/v3/lesson/video?id={lesson_id}",
        # 尝试其他可能的端点
        f"{base_url}/api/v3/lesson/{lesson_id}",
        f"{base_url}/api/v3/video/{lesson_id}",
        f"{base_url}/api/course/video?lesson_id={lesson_id}",
    ]
    
    for i, api_url in enumerate(api_endpoints, 1):
        print(f"\n{'='*60}")
        print(f"🔍 测试API {i}/{len(api_endpoints)}: {api_url}")
        print(f"{'='*60}")
        
        try:
            response = session.get(api_url, headers=headers, cookies=COOKIES)
            print(f"📊 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"📋 响应数据: {data}")
                    
                    # 检查不同的响应格式
                    if data.get('retcode') == 200 and 'result' in data:
                        encrypted_result = data['result']
                        print(f"✅ 找到result字段: {encrypted_result}")
                        
                        # 尝试解密
                        print("🔍 尝试解密...")
                        decrypted = authcode_decrypt(encrypted_result)
                        if decrypted:
                            print(f"✅ 解密成功: {decrypted}")
                            
                            # 检查是否包含视频链接
                            if 'http' in decrypted:
                                print("🎉 可能包含视频链接！")
                                return encrypted_result, decrypted
                        else:
                            print("❌ 解密失败")
                            
                    elif 'data' in data and data['data']:
                        print(f"✅ 找到data字段: {data['data']}")
                    elif 'video_url' in data:
                        print(f"✅ 找到video_url字段: {data['video_url']}")
                    elif 'url' in data:
                        print(f"✅ 找到url字段: {data['url']}")
                    else:
                        print("⚠️ 未找到预期的字段")
                        
                except Exception as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"📄 原始响应: {response.text[:200]}...")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                if response.text:
                    print(f"📄 错误响应: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    print("\n❌ 所有API端点都没有返回有效的视频链接")
    return None, None

if __name__ == "__main__":
    print("🧪 测试新的API调用")
    print("=" * 60)
    
    encrypted_result, decrypted_result = test_new_api_calls()
    
    if encrypted_result and decrypted_result:
        print(f"\n🎉 成功找到视频链接!")
        print(f"🔐 加密结果: {encrypted_result}")
        print(f"🔓 解密结果: {decrypted_result}")
    else:
        print(f"\n💡 建议:")
        print("1. 检查cookies是否有效")
        print("2. 可能需要额外的认证参数")
        print("3. 尝试分析网页的JavaScript代码")
        print("4. 使用浏览器开发者工具查看实际的API调用")
