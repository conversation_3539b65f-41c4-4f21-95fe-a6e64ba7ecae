# 📚 会计学堂课程列表获取器

一个简单的Python程序，用于获取会计学堂网站的课程列表信息。

## 🚀 功能特点

- ✅ 获取已购买/学习的课程列表
- ✅ 显示课程ID、标题、主讲老师、学习进度
- ✅ 简洁清晰的输出格式
- ✅ 自动处理登录状态和重定向

## 📋 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 获取课程列表
```bash
python acc5_simple.py
```

### 3. 获取课程详情
```bash
python acc5_course_details.py
```

### 4. 更新cookies（如果需要）
编辑 `config.py` 文件，将cookies替换为您自己的登录cookies

## 📊 输出示例

```
🔄 正在获取课程列表...

📚 会计学堂课程列表 (20 个课程)

1. [15361] 2025年《中级财务管理》零基础预科课
   👨‍🏫 主讲: 徐莲兆
   📊 进度: 100%

2. [15376] 2025年《中级财务管理》基础精讲课
   👨‍🏫 主讲: 任晓娟
   📊 进度: 74%
...
```

## 🔧 文件说明

- `acc5_simple.py` - 课程列表获取程序（简化版）
- `acc5_course_details.py` - 课程详情获取程序
- `acc5_course_list.py` - 完整版程序（包含调试功能）
- `config.py` - 配置文件（cookies设置）
- `requirements.txt` - Python依赖包
- `debug_page.html` - 调试页面（运行程序后生成）
- `README.md` - 使用说明文档

## ⚠️ 注意事项

1. **Cookies配置**：程序中已包含登录cookies，如果失效需要更新
2. **网络环境**：需要能访问会计学堂网站
3. **依赖包**：确保安装了requests和beautifulsoup4

## 🔍 工作原理

1. 先访问个人中心页面建立会话
2. 访问课程播放页面获取HTML内容
3. 解析HTML提取课程信息
4. 格式化输出课程列表

## 🛠️ 技术栈

- Python 3.x
- requests - HTTP请求
- BeautifulSoup4 - HTML解析

## 🏗️ 项目架构

```
📁 项目结构
├── acc5_simple.py          # 课程列表获取（简化版）
├── acc5_course_details.py  # 课程详情获取
├── acc5_course_list.py     # 完整版（调试功能）
├── config.py               # 配置文件
├── requirements.txt        # 依赖包
└── README.md              # 说明文档

🔄 工作流程
1. 先访问个人中心建立会话
2. 获取课程列表页面
3. 解析HTML提取课程信息
4. 可选择获取具体课程详情
```

## 🚀 后续开发计划

- [x] 添加视频下载功能
- [ ] 支持批量下载课程
- [ ] 添加进度保存功能
- [ ] 支持断点续传
- [ ] 添加GUI界面
- [ ] 支持多账号管理
- [ ] 优化视频解密算法

## 📝 更新日志

- v1.0 - 初始版本，成功获取课程列表
- v1.1 - 添加课程详情获取功能
- v1.2 - 优化配置管理，支持自定义cookies
- v1.3 - 添加视频下载功能，支持多重解密方法
