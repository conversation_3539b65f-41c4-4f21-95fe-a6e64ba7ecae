#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载功能 - 独立的测试脚本
"""

import requests
from config import COOKIES
import base64

def test_video_link_parsing(encrypted_url=None):
    """测试视频链接解析"""
    if not encrypted_url:
        # 从之前的API调用获取的加密字符串
        encrypted_url = "52fbxHHBtv4BqpiyltlXvYNSwmCqempFyKrJm05iMW8Vr+r5SQdm7MNmlebYZJ4l6zTGxQyCxWQ1wwSf4irfUi7c0mE1eRts5t891CunB0RF0de8YJhj005yTsZYIO5ooiWIHojSEFqRv1QKq+0htpzE/98b+IBBKG2zZbhoPmocInZ5/GnE9JRv9gVHjBDVdUWH0bF8YyI"

    print("🔐 原始加密字符串:")
    print(encrypted_url)
    print(f"长度: {len(encrypted_url)} 字符")

    # 尝试使用新的authcode解密
    from acc5_simple import authcode_decrypt
    try:
        decrypted = authcode_decrypt(encrypted_url)
        if decrypted:
            print("\n✅ authcode解密成功:")
            print(decrypted)

            # 尝试解析JSON
            try:
                import json
                json_data = json.loads(decrypted)
                print("\n🎯 JSON解析成功:")
                print(json.dumps(json_data, indent=2, ensure_ascii=False))
                return json_data
            except:
                print("⚠️ 不是有效的JSON格式")
                return decrypted
        else:
            print("❌ authcode解密失败")
    except Exception as e:
        print(f"❌ authcode解密出错: {e}")

    # 尝试Base64解码
    try:
        decoded = base64.b64decode(encrypted_url)
        potential_url = decoded.decode('utf-8')
        print("\n✅ Base64解码结果:")
        print(potential_url)

        if potential_url.startswith('http'):
            print("🎉 解码成功！这是一个有效的HTTP链接")
        else:
            print("⚠️  解码成功，但不是HTTP链接")
    except Exception as e:
        print(f"\n❌ Base64解码失败: {e}")

    # 检查可能的其他编码方式
    print("\n🔍 检查其他可能的编码方式...")
    print(f"包含'/'字符: {'/' in encrypted_url}")
    print(f"包含'.'字符: {'.' in encrypted_url}")
    print(f"包含'http': {'http' in encrypted_url}")

    return None

def test_api_call():
    """测试API调用"""
    session = requests.Session()
    base_url = 'https://m.acc5.com'
    lesson_id = '94603'

    headers = {
        'accept': 'application/json, text/javascript, */*; q=0.01',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'referer': f'https://m.acc5.com/course/course_15361/learn/lesson_{lesson_id}/',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',
        'x-requested-with': 'XMLHttpRequest',
        'priority': 'u=1, i'
    }

    print("\n🌐 测试API调用...")
    print(f"目标URL: {base_url}/api/v3/course/get_comment?lesson_id={lesson_id}")

    # 先访问个人中心
    try:
        home_response = session.get(f'{base_url}/home/', headers=headers, cookies=COOKIES)
        print(f"个人中心访问: {home_response.status_code}")
    except Exception as e:
        print(f"个人中心访问失败: {e}")
        return

    # 获取视频链接
    try:
        api_url = f'{base_url}/api/v3/course/get_comment?lesson_id={lesson_id}'
        response = session.get(api_url, headers=headers, cookies=COOKIES)

        print(f"API响应状态: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                print(f"API响应数据: {data}")

                if data.get('retcode') == 200 and 'result' in data:
                    encrypted_url = data['result']
                    print("\n🎯 获取到加密URL:")
                    print(encrypted_url)

                    # 立即测试解密
                    print("\n🔍 立即测试解密...")
                    test_video_link_parsing(encrypted_url)

                    return encrypted_url
            except Exception as e:
                print(f"JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"API调用失败: {response.text}")

    except Exception as e:
        print(f"API调用异常: {e}")

    return None

if __name__ == "__main__":
    print("🧪 视频下载功能测试")
    print("=" * 50)

    # 测试API调用
    encrypted_url = test_api_call()

    if not encrypted_url:
        print("\n" + "=" * 50)
        print("🔍 分析旧的加密URL")
        print("=" * 50)
        test_video_link_parsing()

    print("\n" + "=" * 50)
    print("💡 建议:")
    print("1. 如果Base64解码失败，可能需要其他解密算法")
    print("2. 检查是否有额外的认证参数")
    print("3. 可能需要分析网页的JavaScript代码来找到解密方法")
    print("4. 尝试使用浏览器开发者工具查看实际的视频请求")
