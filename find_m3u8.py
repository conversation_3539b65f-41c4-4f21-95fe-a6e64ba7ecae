#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门查找m3u8链接
"""

import requests
from config import COOKIES
import re
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def analyze_page_content_for_m3u8():
    """详细分析页面内容查找m3u8"""
    
    # 读取之前保存的页面内容
    try:
        with open('page_content.html', 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        print("❌ 无法读取page_content.html，请先运行extract_video_from_page.py")
        return
    
    print("🔍 详细分析页面内容查找m3u8...")
    print(f"📄 页面大小: {len(content)} 字符")
    
    # 更精确的m3u8搜索模式
    m3u8_patterns = [
        # 完整的m3u8 URL
        r'https?://[^"\s\']+\.m3u8[^"\s\']*',
        
        # 可能的m3u8路径
        r'["\']([^"\']*\.m3u8[^"\']*)["\']',
        r'["\']([^"\']*m3u8[^"\']*)["\']',
        
        # JavaScript变量中的m3u8
        r'var\s+\w*[Uu]rl\s*=\s*["\']([^"\']*m3u8[^"\']*)["\']',
        r'\w*[Uu]rl\s*:\s*["\']([^"\']*m3u8[^"\']*)["\']',
        
        # 可能的HLS相关
        r'["\']([^"\']*hls[^"\']*)["\']',
        r'["\']([^"\']*playlist[^"\']*\.m3u8[^"\']*)["\']',
        
        # 数据属性中的m3u8
        r'data-[^=]*=\s*["\']([^"\']*m3u8[^"\']*)["\']',
        
        # 可能在JSON中
        r'"[^"]*url[^"]*"\s*:\s*"([^"]*m3u8[^"]*)"',
    ]
    
    found_m3u8 = []
    
    for pattern in m3u8_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple):
                match = match[0]
            if match and 'm3u8' in match.lower():
                if match not in found_m3u8:
                    found_m3u8.append(match)
                    print(f"  ✅ 找到m3u8: {match}")
    
    # 查找可能的视频服务器域名
    print("\n🔍 查找视频服务器域名...")
    server_patterns = [
        r'https?://[^"\s\']*video[^"\s\']*\.[^"\s\']*',
        r'https?://[^"\s\']*stream[^"\s\']*\.[^"\s\']*',
        r'https?://[^"\s\']*cdn[^"\s\']*\.[^"\s\']*',
        r'https?://[^"\s\']*media[^"\s\']*\.[^"\s\']*',
        r'https?://v\d*\.[^"\s\']*',
        r'https?://[^"\s\']*\.acc5\.com[^"\s\']*',
    ]
    
    video_servers = set()
    for pattern in server_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            if match not in video_servers:
                video_servers.add(match)
                print(f"  🌐 视频服务器: {match}")
    
    # 查找可能的播放器配置
    print("\n🔍 查找播放器配置...")
    player_patterns = [
        r'player\s*[=:]\s*\{[^}]*\}',
        r'video\s*[=:]\s*\{[^}]*\}',
        r'config\s*[=:]\s*\{[^}]*\}',
        r'options\s*[=:]\s*\{[^}]*\}',
    ]
    
    for pattern in player_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            if 'url' in match.lower() or 'src' in match.lower():
                print(f"  ⚙️ 播放器配置: {match[:100]}...")
    
    return found_m3u8

def use_selenium_to_find_m3u8():
    """使用Selenium模拟浏览器查找m3u8"""
    print("\n🌐 使用Selenium模拟浏览器...")
    
    try:
        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1')
        
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        # 设置cookies
        driver.get("https://m.acc5.com")
        for name, value in COOKIES.items():
            driver.add_cookie({'name': name, 'value': value})
        
        # 访问课程页面
        course_url = "https://m.acc5.com/course/course_15361/learn/lesson_94601/"
        print(f"🔄 访问: {course_url}")
        driver.get(course_url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 获取页面源码
        page_source = driver.page_source
        
        # 查找m3u8
        m3u8_pattern = r'https?://[^"\s\']+\.m3u8[^"\s\']*'
        m3u8_matches = re.findall(m3u8_pattern, page_source, re.IGNORECASE)
        
        if m3u8_matches:
            print("✅ 在Selenium页面中找到m3u8:")
            for match in m3u8_matches:
                print(f"  - {match}")
        else:
            print("❌ 在Selenium页面中未找到m3u8")
        
        # 查找所有可能的视频相关元素
        try:
            video_elements = driver.find_elements(By.TAG_NAME, "video")
            source_elements = driver.find_elements(By.TAG_NAME, "source")
            
            print(f"\n📺 找到 {len(video_elements)} 个video元素")
            for i, elem in enumerate(video_elements):
                src = elem.get_attribute('src')
                if src:
                    print(f"  video[{i}].src: {src}")
            
            print(f"📺 找到 {len(source_elements)} 个source元素")
            for i, elem in enumerate(source_elements):
                src = elem.get_attribute('src')
                if src:
                    print(f"  source[{i}].src: {src}")
        except Exception as e:
            print(f"❌ 查找视频元素失败: {e}")
        
        # 执行JavaScript查找视频链接
        try:
            js_code = """
            var videoLinks = [];
            
            // 查找所有可能包含视频链接的变量
            for (var prop in window) {
                try {
                    var value = window[prop];
                    if (typeof value === 'string' && value.includes('.m3u8')) {
                        videoLinks.push(prop + ': ' + value);
                    }
                } catch(e) {}
            }
            
            // 查找DOM中的视频链接
            var allElements = document.querySelectorAll('*');
            for (var i = 0; i < allElements.length; i++) {
                var elem = allElements[i];
                for (var j = 0; j < elem.attributes.length; j++) {
                    var attr = elem.attributes[j];
                    if (attr.value && attr.value.includes('.m3u8')) {
                        videoLinks.push(attr.name + ': ' + attr.value);
                    }
                }
            }
            
            return videoLinks;
            """
            
            video_links = driver.execute_script(js_code)
            if video_links:
                print("\n🔍 JavaScript查找结果:")
                for link in video_links:
                    print(f"  - {link}")
            else:
                print("\n❌ JavaScript未找到m3u8链接")
                
        except Exception as e:
            print(f"❌ JavaScript执行失败: {e}")
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Selenium失败: {e}")
        print("💡 请确保已安装Chrome浏览器和chromedriver")

def monitor_network_requests():
    """监控网络请求（需要额外的工具）"""
    print("\n📡 网络请求监控建议:")
    print("1. 使用浏览器开发者工具的Network标签")
    print("2. 过滤Media类型的请求")
    print("3. 查找.m3u8或.ts文件")
    print("4. 可能需要点击播放按钮才能触发视频加载")
    print("5. 注意XHR/Fetch请求中可能包含视频链接")

if __name__ == "__main__":
    print("🎯 专门查找m3u8链接")
    print("=" * 60)
    
    # 分析已保存的页面内容
    found_m3u8 = analyze_page_content_for_m3u8()
    
    if found_m3u8:
        print(f"\n🎉 在页面内容中找到 {len(found_m3u8)} 个m3u8链接:")
        for link in found_m3u8:
            print(f"  - {link}")
    else:
        print("\n❌ 在静态页面内容中未找到m3u8链接")
        print("💡 m3u8链接可能是动态生成的")
    
    # 尝试使用Selenium
    try:
        use_selenium_to_find_m3u8()
    except ImportError:
        print("\n⚠️ 未安装selenium，跳过浏览器模拟")
        print("💡 可以运行: pip install selenium")
    
    # 提供网络监控建议
    monitor_network_requests()
    
    print(f"\n💡 为什么您看到m3u8而我看到mp4:")
    print("1. 网站可能根据User-Agent提供不同格式")
    print("2. m3u8可能通过JavaScript动态加载")
    print("3. 需要用户交互（如点击播放）才加载m3u8")
    print("4. 不同的网络环境可能获取不同的视频源")
    print("5. 建议使用浏览器开发者工具直接查看Network请求")
