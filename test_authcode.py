#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试authcode解密功能
"""

from acc5_simple import authcode_decrypt

def test_authcode_decrypt():
    """测试authcode解密函数"""
    # 从之前的API调用获取的加密字符串
    encrypted = "52fbxHHBtv4BqpiyltlXvYNSwmCqempFyKrJm05iMW8Vr+r5SQdm7MNmlebYZJ4l6zTGxQyCxWQ1wwSf4irfUi7c0mE1eRts5t891CunB0RF0de8YJhj005yTsZYIO5ooiWIHojSEFqRv1QKq+0htpzE/98b+IBBKG2zZbhoPmocInZ5/GnE9JRv9gVHjBDVdUWH0bF8YyI"

    print("🔐 测试authcode解密")
    print(f"原始加密字符串: {encrypted}")
    print(f"长度: {len(encrypted)} 字符")

    # 测试解密
    decrypted = authcode_decrypt(encrypted)

    if decrypted:
        print("\n✅ 解密成功!")
        print(f"解密结果: {decrypted}")

        # 检查是否是有效的URL
        if decrypted.startswith('http'):
            print("🎉 这是一个有效的HTTP链接!")
            return decrypted
        else:
            print("⚠️ 解密成功，但不是HTTP链接")
            return decrypted
    else:
        print("❌ 解密失败")
        return None

def test_with_sample_data():
    """使用示例数据测试"""
    print("\n🧪 使用示例数据测试...")
    # 这里可以添加一些已知的加密/解密对来进行测试
    # 由于我们没有已知的测试对，我们只能测试实际的数据
    pass

if __name__ == "__main__":
    print("🔍 authcode解密功能测试")
    print("=" * 50)

    result = test_authcode_decrypt()

    if result:
        print("\n📊 测试结果:")
        print(f"解密后的内容: {result[:100]}{'...' if len(result) > 100 else ''}")

        # 尝试解析JSON
        try:
            import json
            json_data = json.loads(result)
            print("🎯 解析为JSON成功:")
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
        except:
            print("⚠️ 不是有效的JSON格式")

    print("\n" + "=" * 50)
    print("💡 后续步骤:")
    print("1. 如果解密成功，尝试下载视频")
    print("2. 如果解密失败，可能需要调整算法")
    print("3. 检查是否还有其他加密层")
    print("4. 验证解密后的URL是否可访问")
