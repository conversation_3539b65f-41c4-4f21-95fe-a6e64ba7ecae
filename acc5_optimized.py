#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会计学堂视频下载器 - 优化版本
支持m3u8视频流的多线程下载和合并
"""

import requests
import hashlib
import time
import os
import tempfile
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed
from config import COOKIES

def md5(s):
    """计算MD5哈希值"""
    return hashlib.md5(s.encode('utf-8')).hexdigest()

def get_course_list():
    """获取课程列表"""
    session = requests.Session()
    
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "referer": "https://m.acc5.com/home/",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
        "x-requested-with": "XMLHttpRequest"
    }
    
    # 先访问主页建立会话
    session.get("https://m.acc5.com/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)
    
    # 获取课程列表
    api_url = "https://m.acc5.com/api/v3/course/list?page=1&limit=20"
    response = session.get(api_url, headers=headers, cookies=COOKIES)
    
    if response.status_code == 200:
        try:
            data = response.json()
            if data.get('retcode') == 200 and 'result' in data:
                courses = data['result'].get('data', [])
                return courses
        except Exception as e:
            print(f"解析课程列表失败: {e}")
    
    return []

def get_course_details(course_url):
    """获取课程详情"""
    session = requests.Session()
    
    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********"
    }
    
    response = session.get(course_url, headers=headers, cookies=COOKIES)
    
    if response.status_code == 200:
        import re
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 提取课程信息
        course_info = {
            'title': '',
            'teacher': '',
            'description': '',
            'lessons': []
        }
        
        # 查找课程标题
        title_elem = soup.find('h1') or soup.find('title')
        if title_elem:
            course_info['title'] = title_elem.get_text().strip()
        
        # 查找章节列表
        lesson_pattern = r'第(\d+)章|第(\d+)节|(\d+)、'
        lesson_elements = soup.find_all(['li', 'div'], string=re.compile(lesson_pattern))
        
        for i, elem in enumerate(lesson_elements[:10], 1):  # 限制最多10个章节
            lesson_text = elem.get_text().strip()
            if lesson_text:
                course_info['lessons'].append({
                    'index': i,
                    'title': lesson_text,
                    'duration': '未知'
                })
        
        return course_info
    
    return None

def get_unifyplay_id_from_page(course_id, lesson_id):
    """从课程页面提取unifyplay ID"""
    session = requests.Session()
    
    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }
    
    # 建立会话
    session.get("https://m.acc5.com/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)
    
    # 访问课程页面
    lesson_url = f"https://m.acc5.com/course/course_{course_id}/learn/lesson_{lesson_id}/"
    response = session.get(lesson_url, headers=headers, cookies=COOKIES)
    
    if response.status_code != 200:
        print(f"❌ 无法访问课程页面: {response.status_code}")
        return None
    
    # 查找unifyplay链接
    import re
    unifyplay_pattern = r'https://v\.acc5\.com/unifyplay_(\d+)'
    matches = re.findall(unifyplay_pattern, response.text)
    
    if matches:
        unifyplay_id = matches[0]
        print(f"✅ 找到unifyplay ID: {unifyplay_id}")
        return unifyplay_id
    else:
        print("❌ 未找到unifyplay ID，可能不是视频文件")
        return None

def get_video_url(lesson_id, course_id="15361"):
    """获取视频下载链接 - 使用unifyplay重定向方法"""
    print(f"🔍 获取视频链接: lesson_id={lesson_id}, course_id={course_id}")
    
    # 第一步：从页面获取unifyplay ID
    unifyplay_id = get_unifyplay_id_from_page(course_id, lesson_id)
    if not unifyplay_id:
        return None
    
    # 第二步：请求unifyplay URL获取重定向的m3u8链接
    session = requests.Session()
    
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "priority": "i",
        "range": "bytes=0-",
        "referer": "https://m.acc5.com/",
        "sec-ch-ua": '"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "video",
        "sec-fetch-mode": "no-cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }
    
    unifyplay_url = f"https://v.acc5.com/unifyplay_{unifyplay_id}"
    print(f"🔄 请求unifyplay URL: {unifyplay_url}")
    
    try:
        # 不跟随重定向，获取重定向的目标URL
        response = session.get(unifyplay_url, headers=headers, cookies=COOKIES, allow_redirects=False)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code in [301, 302, 303, 307, 308]:
            # 获取重定向的目标URL
            redirect_url = response.headers.get('Location')
            if redirect_url:
                print(f"✅ 获取到重定向URL: {redirect_url}")
                
                # 检查是否是m3u8链接
                if '.m3u8' in redirect_url:
                    print("🎉 找到m3u8链接!")
                    return redirect_url
                else:
                    print("⚠️ 重定向URL不是m3u8格式")
                    return redirect_url
            else:
                print("❌ 重定向响应中没有Location头")
        else:
            print(f"❌ 意外的响应状态: {response.status_code}")
            print(f"📄 响应内容: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 请求unifyplay URL失败: {e}")
    
    return None

def parse_m3u8(m3u8_url):
    """解析m3u8文件，获取所有ts片段的URL"""
    try:
        headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "referer": "https://m.acc5.com/",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
        }
        
        response = requests.get(m3u8_url, headers=headers, cookies=COOKIES, timeout=30)
        response.raise_for_status()
        
        m3u8_content = response.text
        print(f"📋 m3u8内容长度: {len(m3u8_content)} 字符")
        
        # 解析m3u8文件
        lines = m3u8_content.strip().split('\n')
        ts_urls = []
        base_url = '/'.join(m3u8_url.split('/')[:-1]) + '/'
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                # 如果是相对路径，转换为绝对路径
                if line.startswith('http'):
                    ts_urls.append(line)
                else:
                    ts_urls.append(base_url + line)
        
        print(f"🎬 找到 {len(ts_urls)} 个视频片段")
        return ts_urls
        
    except Exception as e:
        print(f"❌ 解析m3u8失败: {e}")
        return []

def download_ts_segment(ts_url, segment_index, temp_dir):
    """下载单个ts片段"""
    try:
        headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "referer": "https://m.acc5.com/",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
        }

        response = requests.get(ts_url, headers=headers, cookies=COOKIES, timeout=30)
        response.raise_for_status()

        # 保存ts片段
        segment_filename = f"segment_{segment_index:04d}.ts"
        segment_path = os.path.join(temp_dir, segment_filename)

        with open(segment_path, 'wb') as f:
            f.write(response.content)

        return segment_path, len(response.content)

    except Exception as e:
        print(f"❌ 下载片段 {segment_index} 失败: {e}")
        return None, 0

def download_video(m3u8_url, filename):
    """下载m3u8视频 - 支持多线程下载ts片段并合并"""
    try:
        print(f"📥 开始下载: {filename}")
        print(f"🔗 m3u8链接: {m3u8_url}")

        # 验证URL格式
        if not m3u8_url.startswith('http'):
            print("❌ 无效的视频链接格式")
            return False

        # 解析m3u8文件
        ts_urls = parse_m3u8(m3u8_url)
        if not ts_urls:
            print("❌ 无法解析m3u8文件或没有找到视频片段")
            return False

        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix='acc5_download_')
        print(f"📁 临时目录: {temp_dir}")

        try:
            # 多线程下载ts片段
            print(f"🚀 开始多线程下载 {len(ts_urls)} 个片段...")

            downloaded_segments = {}
            total_size = 0

            with ThreadPoolExecutor(max_workers=8) as executor:
                # 提交所有下载任务
                future_to_index = {
                    executor.submit(download_ts_segment, ts_url, i, temp_dir): i
                    for i, ts_url in enumerate(ts_urls)
                }

                # 收集下载结果
                completed = 0
                for future in as_completed(future_to_index):
                    segment_index = future_to_index[future]
                    try:
                        segment_path, segment_size = future.result()
                        if segment_path:
                            downloaded_segments[segment_index] = segment_path
                            total_size += segment_size
                            completed += 1

                            # 显示进度
                            progress = (completed / len(ts_urls)) * 100
                            print(f"\r🔄 下载进度: {progress:.1f}% ({completed}/{len(ts_urls)} 片段)", end='', flush=True)
                        else:
                            print(f"\n❌ 片段 {segment_index} 下载失败")
                    except Exception as e:
                        print(f"\n❌ 片段 {segment_index} 处理异常: {e}")

            print(f"\n📊 总下载大小: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")

            # 检查是否所有片段都下载成功
            if len(downloaded_segments) != len(ts_urls):
                print(f"⚠️ 只下载了 {len(downloaded_segments)}/{len(ts_urls)} 个片段")

            # 按顺序合并ts片段
            print("🔗 正在合并视频片段...")

            os.makedirs('downloads', exist_ok=True)
            output_path = os.path.join('downloads', filename)

            with open(output_path, 'wb') as output_file:
                for i in range(len(ts_urls)):
                    if i in downloaded_segments:
                        segment_path = downloaded_segments[i]
                        with open(segment_path, 'rb') as segment_file:
                            shutil.copyfileobj(segment_file, output_file)
                    else:
                        print(f"⚠️ 跳过缺失的片段 {i}")

            print(f"✅ 视频合并完成: {output_path}")

            # 获取最终文件大小
            final_size = os.path.getsize(output_path)
            print(f"📊 最终文件大小: {final_size:,} bytes ({final_size/1024/1024:.1f} MB)")

            return True

        finally:
            # 清理临时文件
            try:
                shutil.rmtree(temp_dir)
                print("🧹 临时文件已清理")
            except:
                print(f"⚠️ 无法清理临时目录: {temp_dir}")

    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        import traceback
        print(f"📋 详细错误: {traceback.format_exc()}")
        return False

def select_and_show_course_details(courses):
    """让用户选择课程并显示详情"""
    while True:
        try:
            choice = input("\n请选择课程序号 (1-20) 或按回车退出: ").strip()

            if not choice:
                print("👋 再见！")
                break

            index = int(choice) - 1
            if 0 <= index < len(courses):
                selected_course = courses[index]
                course_url = f"https://m.acc5.com/course/course_{selected_course['id']}/learn/lesson_1"

                print(f"\n🔄 正在获取课程详情: {selected_course['title']}")
                course_info = get_course_details(course_url)

                if course_info:
                    print(f"\n📖 课程详情: {course_info['title']}")
                    print("=" * 60)
                    print(f"👨‍🏫 主讲老师: {selected_course.get('teacher', '未知')}")
                    print(f"📊 课程信息: 共{len(course_info['lessons'])}节课")
                    print(f"📝 课程简介: {selected_course.get('description', '暂无简介')}")

                    print(f"\n📋 章节列表 ({len(course_info['lessons'])} 个章节):")
                    print("-" * 60)
                    for i, lesson in enumerate(course_info['lessons'], 1):
                        print(f" {i}. {lesson['title']}")
                        print(f"   ⏱️  时长: {lesson.get('duration', '未知')}")

                    # 询问是否下载视频
                    download_choice = input(f"\n💡 是否下载视频？输入章节序号下载 (1-{len(course_info['lessons'])}) 或回车跳过: ").strip()

                    if download_choice and download_choice.isdigit():
                        lesson_index = int(download_choice) - 1
                        if 0 <= lesson_index < len(course_info['lessons']):
                            lesson = course_info['lessons'][lesson_index]
                            lesson_id = f"9460{lesson['index']}"  # 构造lesson_id

                            print(f"🎬 准备下载: {lesson['title']}")

                            # 获取视频链接
                            video_url = get_video_url(lesson_id, selected_course['id'])
                            if video_url:
                                filename = f"{selected_course['id']}_{lesson['title']}.mp4"
                                download_video(video_url, filename)
                            else:
                                print("❌ 获取视频链接失败")
                        else:
                            print("❌ 无效的章节序号")
                else:
                    print("❌ 获取课程详情失败")
            else:
                print(f"❌ 无效序号，请输入 1-{len(courses)} 之间的数字")

        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 用户取消，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    print("🎓 会计学堂视频下载器 - 优化版本")
    print("=" * 60)

    print("🔄 正在获取课程列表...")
    courses = get_course_list()

    if not courses:
        print("❌ 无法获取课程列表，请检查网络连接和cookies配置")
        return

    print(f"\n📚 会计学堂课程列表 ({len(courses)} 个课程)")
    print()

    for i, course in enumerate(courses, 1):
        print(f"{i}. [{course['id']}] {course['title']}")
        print(f"   👨‍🏫 主讲: {course.get('teacher', '未知')}")
        print(f"   📊 进度: {course.get('progress', '0')}%")
        print()

    select_and_show_course_details(courses)

if __name__ == "__main__":
    main()
