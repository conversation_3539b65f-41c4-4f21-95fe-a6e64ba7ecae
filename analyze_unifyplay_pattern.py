#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析unifyplay ID的生成规律
"""

import requests
from config import COOKIES
import re
import time

def get_unifyplay_ids_from_course():
    """从课程页面获取所有章节的unifyplay ID"""
    session = requests.Session()
    
    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"
    }
    
    # 建立会话
    session.get("https://m.acc5.com/home/", headers=headers, cookies=COOKIES)
    time.sleep(1)
    
    course_id = "15361"
    
    # 我们知道的lesson_id列表（从之前的课程详情中获取）
    lesson_ids = [
        "94601",  # 1、开学典礼
        "94602",  # 2、全章框架：一图解财管、了解"财务管理"
        "94603",  # 3、货币时间价值
        "94604",  # 4、资本结构与资本成本
        "94605",  # 5、项目评价指标基础知识
        "94606",  # 6、本量利分析、杜邦分析体系与财务比率、结业测评题
        "94607",  # 7、讲义PDF
    ]
    
    unifyplay_data = []
    
    for i, lesson_id in enumerate(lesson_ids, 1):
        print(f"\n🔍 分析第{i}个视频: lesson_id={lesson_id}")
        
        # 访问课程页面
        lesson_url = f"https://m.acc5.com/course/course_{course_id}/learn/lesson_{lesson_id}/"
        print(f"📄 访问: {lesson_url}")
        
        try:
            response = session.get(lesson_url, headers=headers, cookies=COOKIES)
            
            if response.status_code == 200:
                # 查找unifyplay链接
                unifyplay_pattern = r'https://v\.acc5\.com/unifyplay_(\d+)'
                matches = re.findall(unifyplay_pattern, response.text)
                
                if matches:
                    unifyplay_id = matches[0]
                    print(f"✅ 找到unifyplay ID: {unifyplay_id}")
                    
                    unifyplay_data.append({
                        'lesson_index': i,
                        'lesson_id': lesson_id,
                        'unifyplay_id': unifyplay_id
                    })
                else:
                    print("❌ 未找到unifyplay ID")
                    
                    # 尝试查找其他可能的视频链接模式
                    video_patterns = [
                        r'https://v\.acc5\.com/[^"\']*',
                        r'unifyplay[^"\']*',
                        r'video[^"\']*',
                        r'play[^"\']*',
                    ]
                    
                    for pattern in video_patterns:
                        matches = re.findall(pattern, response.text, re.IGNORECASE)
                        if matches:
                            print(f"🔍 找到相关链接: {matches[:3]}")  # 只显示前3个
            else:
                print(f"❌ 访问失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    return unifyplay_data

def analyze_id_patterns(unifyplay_data):
    """分析ID生成规律"""
    print(f"\n📊 分析ID生成规律")
    print("=" * 60)
    
    if not unifyplay_data:
        print("❌ 没有数据可分析")
        return
    
    print("📋 收集到的数据:")
    for data in unifyplay_data:
        lesson_id = int(data['lesson_id'])
        unifyplay_id = int(data['unifyplay_id'])
        diff = unifyplay_id - lesson_id
        
        print(f"  第{data['lesson_index']}节: lesson_id={data['lesson_id']}, unifyplay_id={data['unifyplay_id']}, 差值={diff}")
    
    # 分析规律
    print(f"\n🔍 规律分析:")
    
    # 检查是否有固定的差值
    if len(unifyplay_data) >= 2:
        diffs = []
        for data in unifyplay_data:
            lesson_id = int(data['lesson_id'])
            unifyplay_id = int(data['unifyplay_id'])
            diff = unifyplay_id - lesson_id
            diffs.append(diff)
        
        if all(d == diffs[0] for d in diffs):
            print(f"✅ 发现固定差值规律: unifyplay_id = lesson_id + {diffs[0]}")
        else:
            print(f"⚠️ 差值不固定: {diffs}")
    
    # 检查是否有其他数学关系
    lesson_ids = [int(data['lesson_id']) for data in unifyplay_data]
    unifyplay_ids = [int(data['unifyplay_id']) for data in unifyplay_data]
    
    print(f"📈 lesson_id范围: {min(lesson_ids)} - {max(lesson_ids)}")
    print(f"📈 unifyplay_id范围: {min(unifyplay_ids)} - {max(unifyplay_ids)}")
    
    # 检查是否是连续的
    lesson_consecutive = all(lesson_ids[i] == lesson_ids[i-1] + 1 for i in range(1, len(lesson_ids)))
    unifyplay_consecutive = all(unifyplay_ids[i] == unifyplay_ids[i-1] + 1 for i in range(1, len(unifyplay_ids)))
    
    print(f"🔢 lesson_id是否连续: {lesson_consecutive}")
    print(f"🔢 unifyplay_id是否连续: {unifyplay_consecutive}")

def test_unifyplay_redirect(unifyplay_id):
    """测试unifyplay重定向"""
    print(f"\n🧪 测试unifyplay重定向: {unifyplay_id}")
    
    session = requests.Session()
    
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "priority": "i",
        "range": "bytes=0-",
        "referer": "https://m.acc5.com/",
        "sec-fetch-dest": "video",
        "sec-fetch-mode": "no-cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0"
    }
    
    unifyplay_url = f"https://v.acc5.com/unifyplay_{unifyplay_id}"
    
    try:
        # 不跟随重定向
        response = session.get(unifyplay_url, headers=headers, cookies=COOKIES, allow_redirects=False)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code in [301, 302, 303, 307, 308]:
            redirect_url = response.headers.get('Location')
            if redirect_url:
                print(f"✅ 重定向到: {redirect_url}")
                
                if '.m3u8' in redirect_url:
                    print("🎉 成功获取m3u8链接!")
                    return redirect_url
                else:
                    print("⚠️ 重定向URL不是m3u8格式")
            else:
                print("❌ 没有Location头")
        else:
            print(f"❌ 意外状态码: {response.status_code}")
            print(f"📄 响应: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return None

if __name__ == "__main__":
    print("🎯 分析unifyplay ID生成规律")
    print("=" * 60)
    
    # 获取所有章节的unifyplay ID
    unifyplay_data = get_unifyplay_ids_from_course()
    
    if unifyplay_data:
        # 分析规律
        analyze_id_patterns(unifyplay_data)
        
        # 测试第一个unifyplay ID的重定向
        if unifyplay_data:
            first_unifyplay_id = unifyplay_data[0]['unifyplay_id']
            m3u8_url = test_unifyplay_redirect(first_unifyplay_id)
            
            if m3u8_url:
                print(f"\n🎉 成功获取视频链接!")
                print(f"📺 m3u8链接: {m3u8_url}")
            else:
                print(f"\n❌ 无法获取视频链接")
    else:
        print("❌ 未能获取任何unifyplay数据")
        
        # 如果无法从页面获取，尝试一些可能的ID
        print("\n🔍 尝试一些可能的unifyplay ID...")
        possible_ids = [
            "116965",  # 您提供的ID
            "116821",  # 之前找到的ID
            "116966", "116967", "116968",  # 可能的连续ID
        ]
        
        for test_id in possible_ids:
            m3u8_url = test_unifyplay_redirect(test_id)
            if m3u8_url:
                print(f"🎉 ID {test_id} 有效!")
                break
    
    print(f"\n💡 总结:")
    print("1. 每个视频都有唯一的unifyplay ID")
    print("2. 这个ID嵌入在课程页面的HTML中")
    print("3. 通过请求 https://v.acc5.com/unifyplay_{ID} 获取重定向的m3u8链接")
    print("4. 需要正确的cookies和headers才能成功重定向")
