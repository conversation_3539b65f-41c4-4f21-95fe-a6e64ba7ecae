var debugMode = getStorageByName('debugMode') || 0; //设置调试模式，发布时要设置为false（点击个人中心顶部左边可以开启或者关闭）
var httpMode = getStorageByName('httpMode') || 0; //改用HTTP模式进行调试，默认是false（点击个人中心顶部右边可以开启或者关闭）


//base64 start
function base64_encode(str) {
    var base64EncodeChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    var out, i, len;
    var c1, c2, c3;
    len = str.length;
    i = 0;
    out = "";
    while (i < len) {
        c1 = str.charCodeAt(i++) & 0xff;
        if (i == len) {
            out += base64EncodeChars.charAt(c1 >> 2);
            out += base64EncodeChars.charAt((c1 & 0x3) << 4);
            out += "==";
            break;
        }
        c2 = str.charCodeAt(i++);
        if (i == len) {
            out += base64EncodeChars.charAt(c1 >> 2);
            out += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4));
            out += base64EncodeChars.charAt((c2 & 0xF) << 2);
            out += "=";
            break;
        }
        c3 = str.charCodeAt(i++);
        out += base64EncodeChars.charAt(c1 >> 2);
        out += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4));
        out += base64EncodeChars.charAt(((c2 & 0xF) << 2) | ((c3 & 0xC0) >> 6));
        out += base64EncodeChars.charAt(c3 & 0x3F);
    }
    return out;
}

function base64_decode(str) {
    var base64DecodeChars = new Array(-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1);
    var c1, c2, c3, c4;
    var i, len, out;
    len = str.length;
    i = 0;
    out = "";
    while (i < len) {
        do {
            c1 = base64DecodeChars[str.charCodeAt(i++) & 0xff];
        } while (i < len && c1 == -1);
        if (c1 == -1)
            break;
        do {
            c2 = base64DecodeChars[str.charCodeAt(i++) & 0xff];
        } while (i < len && c2 == -1);
        if (c2 == -1)
            break;
        out += String.fromCharCode((c1 << 2) | ((c2 & 0x30) >> 4));
        do {
            c3 = str.charCodeAt(i++) & 0xff;
            if (c3 == 61)
                return out;
            c3 = base64DecodeChars[c3];
        } while (i < len && c3 == -1);
        if (c3 == -1)
            break;
        out += String.fromCharCode(((c2 & 0XF) << 4) | ((c3 & 0x3C) >> 2));
        do {
            c4 = str.charCodeAt(i++) & 0xff;
            if (c4 == 61)
                return out;
            c4 = base64DecodeChars[c4];
        } while (i < len && c4 == -1);
        if (c4 == -1)
            break;
        out += String.fromCharCode(((c3 & 0x03) << 6) | c4);
    }
    return out;
}

function utf16to8(str) {
    var out, i, len, c;
    out = "";
    len = str.length;
    for (i = 0; i < len; i++) {
        c = str.charCodeAt(i);
        if ((c >= 0x0001) && (c <= 0x007F)) {
            out += str.charAt(i);
        } else if (c > 0x07FF) {
            out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
            out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
            out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
        } else {
            out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
            out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
        }
    }
    return out;
}

function utf8to16(str) {
    var out, i, len, c;
    var char2, char3;
    out = "";
    len = str.length;
    i = 0;
    while (i < len) {
        c = str.charCodeAt(i++);
        switch (c >> 4) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
                out += str.charAt(i - 1);
                break;
            case 12:
            case 13:
                char2 = str.charCodeAt(i++);
                out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
                break;
            case 14:
                char2 = str.charCodeAt(i++);
                char3 = str.charCodeAt(i++);
                out += String.fromCharCode(((c & 0x0F) << 12) | ((char2 & 0x3F) << 6) | ((char3 & 0x3F) << 0));
                break;
        }
    }
    return out;
}
//base64 end

//md5 start
var hexcase = 0;

function hex_md5(a) {
    return rstr2hex(rstr_md5(str2rstr_utf8(a)))
}

function hex_hmac_md5(a, b) {
    return rstr2hex(rstr_hmac_md5(str2rstr_utf8(a), str2rstr_utf8(b)))
}

function md5_vm_test() {
    return hex_md5("abc").toLowerCase() == "900150983cd24fb0d6963f7d28e17f72"
}

function rstr_md5(a) {
    return binl2rstr(binl_md5(rstr2binl(a), a.length * 8))
}

function rstr_hmac_md5(c, f) {
    var e = rstr2binl(c);
    if (e.length > 16) {
        e = binl_md5(e, c.length * 8)
    }
    var a = Array(16),
        d = Array(16);
    for (var b = 0; b < 16; b++) {
        a[b] = e[b] ^ 909522486;
        d[b] = e[b] ^ 1549556828
    }
    var g = binl_md5(a.concat(rstr2binl(f)), 512 + f.length * 8);
    return binl2rstr(binl_md5(d.concat(g), 512 + 128))
}

function rstr2hex(c) {
    try {
        hexcase
    } catch (g) {
        hexcase = 0
    }
    var f = hexcase ? "0123456789ABCDEF" : "0123456789abcdef";
    var b = "";
    var a;
    for (var d = 0; d < c.length; d++) {
        a = c.charCodeAt(d);
        b += f.charAt((a >>> 4) & 15) + f.charAt(a & 15)
    }
    return b
}

function str2rstr_utf8(c) {
    var b = "";
    var d = -1;
    var a, e;
    while (++d < c.length) {
        a = c.charCodeAt(d);
        e = d + 1 < c.length ? c.charCodeAt(d + 1) : 0;
        if (55296 <= a && a <= 56319 && 56320 <= e && e <= 57343) {
            a = 65536 + ((a & 1023) << 10) + (e & 1023);
            d++
        }
        if (a <= 127) {
            b += String.fromCharCode(a)
        } else {
            if (a <= 2047) {
                b += String.fromCharCode(192 | ((a >>> 6) & 31), 128 | (a & 63))
            } else {
                if (a <= 65535) {
                    b += String.fromCharCode(224 | ((a >>> 12) & 15), 128 | ((a >>> 6) & 63), 128 | (a & 63))
                } else {
                    if (a <= 2097151) {
                        b += String.fromCharCode(240 | ((a >>> 18) & 7), 128 | ((a >>> 12) & 63), 128 | ((a >>> 6) & 63), 128 | (a & 63))
                    }
                }
            }
        }
    }
    return b
}

function rstr2binl(b) {
    var a = Array(b.length >> 2);
    for (var c = 0; c < a.length; c++) {
        a[c] = 0
    }
    for (var c = 0; c < b.length * 8; c += 8) {
        a[c >> 5] |= (b.charCodeAt(c / 8) & 255) << (c % 32)
    }
    return a
}

function binl2rstr(b) {
    var a = "";
    for (var c = 0; c < b.length * 32; c += 8) {
        a += String.fromCharCode((b[c >> 5] >>> (c % 32)) & 255)
    }
    return a
}

function binl_md5(p, k) {
    p[k >> 5] |= 128 << ((k) % 32);
    p[(((k + 64) >>> 9) << 4) + 14] = k;
    var o = 1732584193;
    var n = -271733879;
    var m = -1732584194;
    var l = 271733878;
    for (var g = 0; g < p.length; g += 16) {
        var j = o;
        var h = n;
        var f = m;
        var e = l;
        o = md5_ff(o, n, m, l, p[g + 0], 7, -680876936);
        l = md5_ff(l, o, n, m, p[g + 1], 12, -389564586);
        m = md5_ff(m, l, o, n, p[g + 2], 17, 606105819);
        n = md5_ff(n, m, l, o, p[g + 3], 22, -1044525330);
        o = md5_ff(o, n, m, l, p[g + 4], 7, -176418897);
        l = md5_ff(l, o, n, m, p[g + 5], 12, 1200080426);
        m = md5_ff(m, l, o, n, p[g + 6], 17, -1473231341);
        n = md5_ff(n, m, l, o, p[g + 7], 22, -45705983);
        o = md5_ff(o, n, m, l, p[g + 8], 7, 1770035416);
        l = md5_ff(l, o, n, m, p[g + 9], 12, -1958414417);
        m = md5_ff(m, l, o, n, p[g + 10], 17, -42063);
        n = md5_ff(n, m, l, o, p[g + 11], 22, -1990404162);
        o = md5_ff(o, n, m, l, p[g + 12], 7, 1804603682);
        l = md5_ff(l, o, n, m, p[g + 13], 12, -40341101);
        m = md5_ff(m, l, o, n, p[g + 14], 17, -1502002290);
        n = md5_ff(n, m, l, o, p[g + 15], 22, 1236535329);
        o = md5_gg(o, n, m, l, p[g + 1], 5, -165796510);
        l = md5_gg(l, o, n, m, p[g + 6], 9, -1069501632);
        m = md5_gg(m, l, o, n, p[g + 11], 14, 643717713);
        n = md5_gg(n, m, l, o, p[g + 0], 20, -373897302);
        o = md5_gg(o, n, m, l, p[g + 5], 5, -701558691);
        l = md5_gg(l, o, n, m, p[g + 10], 9, 38016083);
        m = md5_gg(m, l, o, n, p[g + 15], 14, -660478335);
        n = md5_gg(n, m, l, o, p[g + 4], 20, -405537848);
        o = md5_gg(o, n, m, l, p[g + 9], 5, 568446438);
        l = md5_gg(l, o, n, m, p[g + 14], 9, -1019803690);
        m = md5_gg(m, l, o, n, p[g + 3], 14, -187363961);
        n = md5_gg(n, m, l, o, p[g + 8], 20, 1163531501);
        o = md5_gg(o, n, m, l, p[g + 13], 5, -1444681467);
        l = md5_gg(l, o, n, m, p[g + 2], 9, -51403784);
        m = md5_gg(m, l, o, n, p[g + 7], 14, 1735328473);
        n = md5_gg(n, m, l, o, p[g + 12], 20, -1926607734);
        o = md5_hh(o, n, m, l, p[g + 5], 4, -378558);
        l = md5_hh(l, o, n, m, p[g + 8], 11, -2022574463);
        m = md5_hh(m, l, o, n, p[g + 11], 16, 1839030562);
        n = md5_hh(n, m, l, o, p[g + 14], 23, -35309556);
        o = md5_hh(o, n, m, l, p[g + 1], 4, -1530992060);
        l = md5_hh(l, o, n, m, p[g + 4], 11, 1272893353);
        m = md5_hh(m, l, o, n, p[g + 7], 16, -155497632);
        n = md5_hh(n, m, l, o, p[g + 10], 23, -1094730640);
        o = md5_hh(o, n, m, l, p[g + 13], 4, 681279174);
        l = md5_hh(l, o, n, m, p[g + 0], 11, -358537222);
        m = md5_hh(m, l, o, n, p[g + 3], 16, -722521979);
        n = md5_hh(n, m, l, o, p[g + 6], 23, 76029189);
        o = md5_hh(o, n, m, l, p[g + 9], 4, -640364487);
        l = md5_hh(l, o, n, m, p[g + 12], 11, -421815835);
        m = md5_hh(m, l, o, n, p[g + 15], 16, 530742520);
        n = md5_hh(n, m, l, o, p[g + 2], 23, -995338651);
        o = md5_ii(o, n, m, l, p[g + 0], 6, -198630844);
        l = md5_ii(l, o, n, m, p[g + 7], 10, 1126891415);
        m = md5_ii(m, l, o, n, p[g + 14], 15, -1416354905);
        n = md5_ii(n, m, l, o, p[g + 5], 21, -57434055);
        o = md5_ii(o, n, m, l, p[g + 12], 6, 1700485571);
        l = md5_ii(l, o, n, m, p[g + 3], 10, -1894986606);
        m = md5_ii(m, l, o, n, p[g + 10], 15, -1051523);
        n = md5_ii(n, m, l, o, p[g + 1], 21, -2054922799);
        o = md5_ii(o, n, m, l, p[g + 8], 6, 1873313359);
        l = md5_ii(l, o, n, m, p[g + 15], 10, -30611744);
        m = md5_ii(m, l, o, n, p[g + 6], 15, -1560198380);
        n = md5_ii(n, m, l, o, p[g + 13], 21, 1309151649);
        o = md5_ii(o, n, m, l, p[g + 4], 6, -145523070);
        l = md5_ii(l, o, n, m, p[g + 11], 10, -1120210379);
        m = md5_ii(m, l, o, n, p[g + 2], 15, 718787259);
        n = md5_ii(n, m, l, o, p[g + 9], 21, -343485551);
        o = safe_add(o, j);
        n = safe_add(n, h);
        m = safe_add(m, f);
        l = safe_add(l, e)
    }
    return Array(o, n, m, l)
}

function md5_cmn(h, e, d, c, g, f) {
    return safe_add(bit_rol(safe_add(safe_add(e, h), safe_add(c, f)), g), d)
}

function md5_ff(g, f, k, j, e, i, h) {
    return md5_cmn((f & k) | ((~f) & j), g, f, e, i, h)
}

function md5_gg(g, f, k, j, e, i, h) {
    return md5_cmn((f & j) | (k & (~j)), g, f, e, i, h)
}

function md5_hh(g, f, k, j, e, i, h) {
    return md5_cmn(f ^ k ^ j, g, f, e, i, h)
}

function md5_ii(g, f, k, j, e, i, h) {
    return md5_cmn(k ^ (f | (~j)), g, f, e, i, h)
}

function safe_add(a, d) {
    var c = (a & 65535) + (d & 65535);
    var b = (a >> 16) + (d >> 16) + (c >> 16);
    return (b << 16) | (c & 65535)
}

function bit_rol(a, b) {
    return (a << b) | (a >>> (32 - b))
};
//md5 end

Date.prototype.format = function(format) {
    var o = {
        "M+": this.getMonth() + 1, //month
        "d+": this.getDate(), //day
        "h+": this.getHours(), //hour
        "m+": this.getMinutes(), //minute
        "s+": this.getSeconds(), //second
        "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
        "S": this.getMilliseconds() //millisecond
    }
    if (/(y+)/.test(format)) format = format.replace(RegExp.$1,
        (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(format))
            format = format.replace(RegExp.$1,
                RegExp.$1.length == 1 ? o[k] :
                ("00" + o[k]).substr(("" + o[k]).length));
    return format;
}

//全局变量
var appId = 'kjxt';
var notClearCache = ['userid', 'username', 'login_phone', 'user_groupid', 'nick', 'user_avatar', 'login_flg', 'user_experience_time', 'user_experience_timestamp', 'videoPath', 'upgrade_status','user_qq','user_order',
    'debugMode', 'httpMode', 'iosDownloadMode', 'exerciseFontSize', 'exerciseNightMode', 'exerciseAnswerShow', 'myPlayerV2', 'coursePlayed', 'lessonPlayed', 'lessonDownloaded', 'practicePre'
];
//OT 的设置
var OT = {
    role_19: 'SVIP',
    role_9: 'VIP用户',
    role_8: 'VIP套餐用户',
    role_2: '体验用户',
    role_1: '普通用户',
    faxian: '发现',
    kecheng: '课程',
    tiku: '答疑',
    me: '我',

    alert_title: '提示', //'提示',
    alert_btnVal: '确定',
    progress_style: 'default',
    progress_animationType: 'fade',
    progress_title: '努力加载中...',

    toast_duration: 2000,
    toast_location: 'bottom',

    keshi: '课时',

    update_pwd: '修改密码',
    logout: '安全退出',
    login: '登录',
    login_account: '登录名',
    full_name: '姓名',
    level: '用户组',

    pwd: '密码',
    seccode: '验证码',
    register: '注册',
    phone: '手机号',
    setPwd: '设置密码',
    conPwd: '确认密码',

    //下载目录变化比较大，如果需要改回原来的需要修改如下：
    //1、去掉config.xml的sandbox配置
    //2、dataType改为.acc5
    //3、path_video改为$api.getStorage('videoPath')==1 ? 'fs://com.apicloud.A6999270760613/' : 'fs://download/'
    dataType: ".dat", //下载文件类型后缀，老版本.acc5，为了不影响IOS需要在编译的时候改回老版本
    dataPath: 'fs://data/com.md.android.info/data/', //下载文件目录，老版本fs://download/，为了不影响IOS需要在编译的时候改回老版本
    dataTypeIOS: ".acc5",
    dataPathIOS: 'fs://download/',

    url_verify_code: 'https://www.acc5.com/module.php?c=verify_code&action=phone_login&type=1&check_phone=1&phone=', //登录时获取验证码，新版加上&check_phone=1参数先去校验手机号码是否绑定
    url_reg_code: 'https://www.acc5.com/module.php?c=verify_code&action=reg&type=1&phone=', //注册时获取验证码
    url_bind_code: 'https://www.acc5.com/module.php?c=verify_code&action=binding&type=1&phone=', //绑定时获取验证码

    url_check_phone_status: 'https://www.acc5.com/module.php?c=check_phone_binding_status&phone=', //检查手机号码是否绑定

    url_register: 'https://www.acc5.com/api.php?encrypt=1&c=app_activate_register', //注册
    url_login: 'https://www.acc5.com/api.php?encrypt=1&c=app_login', //登录
    url_logout: 'https://www.acc5.com/api.php?encrypt=1&c=app_logout',
    url_bind: 'https://www.acc5.com/api.php?c=app_binding',//绑定（手机号码、qq，修改op）
    url_userinfo: 'https://www.acc5.com/api.php?encrypt=1&c=app_userinfo',
    url_order : 'https://www.acc5.com/api.php?c=app_user_order',//订单相关
    
    url_cat: 'https://www.acc5.com/api.php?encrypt=1&c=app_course_cat', //课程nav导航
    url_course_list: 'https://www.acc5.com/api.php?encrypt=1&c=app_course_list&list_num=10&', //列表页数据
    url_course_live_list: 'https://www.acc5.com/api.php?encrypt=1&c=app_course_live_list&list_num=10&', //会计职称的列表页数据
    url_course_view: 'https://www.acc5.com/api.php?encrypt=1&c=app_course_view&has_chapter=1&id=', //课程详细数据
    url_getkey_video: 'https://www.acc5.com/module.php?c=video_auth&op=set&id=',
    url_course_list_hot: 'https://www.acc5.com/api.php?encrypt=1&c=app_course_list',
    url_course_live: 'https://www.acc5.com/api.php?encrypt=1&c=app_course_lesson&type=live',
    url_seccode: 'https://www.acc5.com/module.php?c=seccode&rand=',
    url_question_list: 'https://www.acc5.com/api.php?encrypt=1&c=app_ask',
    url_quetion_view: 'https://www.acc5.com/api.php?encrypt=1&c=app_ask_detail&id=',

    url_replay: 'https://www.acc5.com/api.php?encrypt=1&c=app_course_list&cat_id=2&type=live_trailer', //直播回放
    url_delpic: 'https://www.acc5.com/api.php?encrypt=1&c=app_uploadpic&op=del&id=',
    url_postpic: 'https://www.acc5.com/api.php?encrypt=1&c=app_uploadpic&op=up',

    url_sub_cat: 'https://www.acc5.com/api.php?c=app_course_sub_cat&cat_id=', //二级分类
    url_live_history: 'https://www.acc5.com/api.php?encrypt=1&c=app_course_live_history&list_num=20&page=',
    url_myHistory: 'https://www.acc5.com/api.php?c=app_course_collect&',
    url_favorids: 'https://www.acc5.com/api.php?c=app_course_myfavor',
    url_search: 'https://www.acc5.com/api.php?c=app_search',
    url_phone_login: 'https://www.acc5.com/api.php?c=app_phone_login',
    url_ioscheck: 'http://ioscheck.duapp.com/app/ios/check.json?tt=', //ios审核使用的版本控制文件，先使用HTTP
    url_video_permission: 'https://www.acc5.com/api.php?c=app_video_permission',
    url_question_more: 'https://www.acc5.com/api.php?c=app_ask_list',

    url_tiku_practice_v2: "https://www.acc5.com/api.php?c=app_tiku_section_exam&ver=20161001", //new 章节练习
    url_tiku_exam_v2: "https://www.acc5.com/api.php?c=app_tiku_exam_list&ver=20161001", //new 真题模拟+综合测试
    url_tiku_other_v2: 'https://www.acc5.com/api.php?c=app_tiku_exam_other&ver=20161001', //new 每日特训+我的收藏+我的错题


    url_tiku_practice: 'https://www.acc5.com/api.php?c=app_tiku_practice_new&ver=20161001', //章节练习列表
    url_tiku_other: 'https://www.acc5.com/api.php?c=app_tiku_other_new&ver=20161001', //综合测试、每日特训
    url_tiku_exam: 'https://www.acc5.com/api.php?c=app_tiku_exam_new&ver=20161001', //真题模拟列表

    url_mycourse_list: 'https://www.acc5.com/api.php?c=app_my_course&list_num=0',

    url_test_network: 'https://www.acc5.com/api.php', //检测网络是否正常
    url_test_download: 'https://yun3s.acc5.com/test.txt', //检测下载是否正常

    url_study_plan: 'https://www.acc5.com/api.php?c=app_learn_plan', //获取学习计划信息，提交时添加&op=submit
    url_study_plan_submit: 'https://www.acc5.com/api.php?c=app_learn_plan&op=submit', //提交学习计划信息

    url_error_report: 'http://www.acc5.com/api.php?c=app_feedback', //错误上报接口

    //真账实操
    url_operate_list: 'https://lx.acc5.com/api/v1/course_list', //列表
    url_operate_detail: 'https://lx.acc5.com/api/v1/course_lesson', //详情
    url_operate_info: 'https://lx.acc5.com/api/v1/lesson_info', //做账信息
    url_operate_answer: 'https://lx.acc5.com/api/v1/lesson_answer', //答案
    url_operate_subject: 'https://lx.acc5.com/api/v1/course_subject', //科目
    url_operate_submit: 'https://lx.acc5.com/api/v1/post_exercise_answer', //提交答案

    rand: '',
    //rand:getRand(),
    km: {
        'cy': {
            name: '会计从业',
            cid: [1, 2, 3],
            cn: ['财经法规', '会计基础', '会计电算化']
        },
        'cj': {
            name: '会计初级',
            cid: [4, 5],
            cn: ['经济法基础', '初级会计实务']
        },
        'zj': {
            name: '会计中级',
            cid: [6, 7, 8],
            cn: ['中级会计实务', '中级经济法', '中级财务管理']
        }
    },
    time_arr: {
        '1': 60,
        '2': 60,
        '3': 60,
        '4': 90,
        '5': 120,
        '6': 60,
        '7': 60,
        '8': 60
    },
    menu_arr: {
        '1': '章节练习',
        '2': '综合测试',
        '3': '真题模拟',
        '4': '每日特训',
        '5': '我的收藏',
        '6': '我的错题',
        '7': '学习规划',
        '8': '微信群友',
        '9': '公开课'
    },
    //cid=1,2,3,6,7,8
    score_arr: {
        'ss': 1,
        'mm': 2,
        'pp': 1,
        'bb': 2
    },
    tt_arr: {
        '1': '单选题',
        '2': '多选题',
        '3': '判断题',
        '4': '不定项选择题',
        '5': '填空题',
        '7': '综合题'
    },
    op_arr: {
        '1': {
            list: 'zjlist',
            view: 'zjview',
            sub: 'zjsubmit'
        },
        '2': {
            list: 'kslist',
            view: 'ksview',
            sub: 'kssubmit'
        },
        '3': {
            list: 'kslist',
            view: 'ksview',
            sub: 'kssubmit'
        },
        '4': {
            list: 'txlist',
            view: 'txview',
            sub: 'txsubmit'
        },
        '5': {
            list: 'favorlist',
            view: 'favorview',
            sub: ''
        },
        '6': {
            list: 'errlist',
            view: 'errview',
            sub: ''
        },
    },
};
//获取题库的收藏信息,force为true表示强制更新
function getExerciseFavor(callback, force) {
    if (force !== true && getStorageByName('localFavor')) {
        callback && callback();
        return;
    }
    mAjax(OT.url_tiku_other_v2, function(ret) {
        //console.log("favor=" + JSON.stringify(ret))
        if (ret.code == 200) {
            var str = '';
            if (ret.data[0] && ret.data[0].tm_id.indexOf('@') == -1 && ret.data[0].tm_id.indexOf('undefined') == -1)
                str = ret.data[0].tm_id;
            $api.setStorage('localFavor', str);
        }
        callback && callback();
    }, function() {
        callback && callback();
    }, {
        op: 'my_collects'
    })
};
//添加题库收藏到后台
function setExerciseFavor(tid) {
    setLocalFavor(tid)
    var _data = {
        type: '2',
        op: 'favoradd',
        collect: tid,
    };
    mAjax(OT.url_tiku_other_v2, function(ret) {
        if (ret.code == 200) {
            mToast(isExerciseFavor(tid) ? "收藏成功" : '取消收藏成功');
            $api.setStorage("exercise_favor_changed", 1); //在列表页面获取判断是否刷新
        } else {
            mToast('出错了！' + ret.msg);
        }
    }, function(err) {}, _data);
};
//判断该题是否是收藏的
function isExerciseFavor(tid) {
    var arr = [];
    if ($api.getStorage('localFavor')) arr = $api.getStorage('localFavor').split(',');
    for (var i = 0; i < arr.length; i++) {
        if (arr[i] == tid) {
            return true;
        }
    }
    return false;
};
//设置本地题库题目收藏（或取消）
function setLocalFavor(tid) {
    var arr = [];
    if ($api.getStorage('localFavor')) arr = $api.getStorage('localFavor').split(',');
    for (var i = 0; i < arr.length; i++) {
        if (arr[i] == tid) {
            arr.splice(i, 1);
            var data = arr.join(',');
            $api.setStorage('localFavor', data);
            return data;
        }
    }
    arr.push(tid);
    var data = arr.join(',');
    $api.setStorage('localFavor', data);
    return data;
};
//获取课程收藏信息
function getFavorData() {
    mAjax(OT.url_favorids, function(ret) {
        $api.setStorage('favorids', ret.code == 200 ? ret.data : []);
        api.execScript({
            name: 'course',
            script: 'setFavorStatus();'
        });
        api.execScript({
            name: 'info',
            script: 'setFavorStatus();'
        });

    }, callbackErr, {
        uid: $api.getStorage('userid')
    });
};
//设置课程收藏信息
function setFavorStatus() {
    !isLogined() && $api.clearStorage();
    var arr = $api.getStorage('favorids');
    $('.course .item .favor').removeClass('favored'); //先全部设置为没有收藏
    if (arr == undefined) {
        mAjax(OT.url_favorids, function(ret) {
            $api.setStorage('favorids', ret.code == 200 ? ret.data : []);
            arr = $api.getStorage('favorids');
            for (var i = 0; i < arr.length; i++) {
                $('.course .item[viewid=' + arr[i] + '] .favor').addClass('favored');
            }
        }, callbackErr, {
            uid: $api.getStorage('userid')
        });
    } else {
        for (var i = 0; i < arr.length; i++) {
            $('.course .item[viewid=' + arr[i] + '] .favor').addClass('favored');
        }
    }
};
//课程收藏按钮绑定
function bindClick() {
    setFavorStatus();
    $('.course .item .favor').unbind('click').click(function() {
        if (!isLogined()) {
            mAlert('只有登录用户才能收藏喔', '', '', true);
            return;
        }
        var viewid = $(this).parents('.course .item').attr('viewid');
        var _data = {
            uid: $api.getStorage('userid'),
            type: 'myFavor',
            courseid: viewid
        }
        var ele = $(this);
        mAjax(OT.url_myHistory, function(ret) {
            getCookie('ioscheck') != 1 && mToast(ret.msg); //IOS检测时就不弹出消息（因为消息请登录）
            var arr = $api.getStorage('favorids') || [];
            if (ele.hasClass('favored')) { //已经收藏
                ele.removeClass('favored');
                var index = arr.indexOf(viewid);
                index != -1 && arr.splice(index, 1);
            } else {
                ele.addClass('favored');
                arr.push(viewid);
            }
            $api.setStorage('favorids', arr);
        }, function(err) {}, _data); //收藏时也上传浏览记录，但不去处理缓存
        return false;
    });
};
//时间随机
function getRand() {
    return '?tt=' + new Date().format('yyyy-MM-dd-hh');
};
//通用简单空回调处理函数
function callbackErr(err, msg) {
    api.hideProgress();
    api.refreshHeaderLoadDone();
};
function callbackOK(ret, jsonStr) {};
//客户端缓存，不知道是有什么鬼用
var Cache = {
    hot: null,
    cat: null,
    ad: null,
    replay: null,
    vv: '',
};
function getCache() {
    if ($api.getStorage('cache_hot') && $api.getStorage('cache_hot') != 'undefined') Cache.hot = $api.getStorage('cache_hot');
    if ($api.getStorage('cache_cat') && $api.getStorage('cache_cat') != 'undefined') Cache.cat = $api.getStorage('cache_cat');
    if ($api.getStorage('cache_ad') && $api.getStorage('cache_ad') != 'undefined') Cache.ad = $api.getStorage('cache_ad');
    if ($api.getStorage('cache_replay') && $api.getStorage('cache_replay') != 'undefined') Cache.replay = $api.getStorage('cache_replay');
};
function rmCache() {};
//获取缓存，有过期时间限制
function getCacheById(key) {
    var t2 = new Date().getTime();
    var t1 = $api.getStorage('clsTime') ? $api.getStorage('clsTime') : 100;
    if ((t2 - t1) > 24 * 60 * 60 * 1000) {
        clearCacheExUserInfo();
        $api.setStorage('clsTime', t2);
    }
    if ($api.getStorage('user_groupid') == 2 && $api.getStorage('user_experience_time') <= 0) {
        $api.setStorage('user_experience_time', 0);
        return null;
    }
    if ($api.getStorage(key) && $api.getStorage(key) != 'undefined') return $api.getStorage(key);
    return null;
};
//获取缓存，无过期时间限制，默认返回null
function getStorageByName(key) {
    if ($api.getStorage(key) && $api.getStorage(key) != 'undefined') {
        return $api.getStorage(key)
    };
    return null;
};
//清除用户信息外的其他缓存
function clearCacheExUserInfo() {
    var cache = {};
    notClearCache.forEach(function(v) {
        cache[v] = $api.getStorage(v);
    });
    $api.clearStorage();
    notClearCache.forEach(function(v) {
        $api.setStorage(v, cache[v]);
    });
};
//清楚用户缓存
function rmUserInfo() {
    $api.rmStorage('userid');
    $api.rmStorage('username');
    $api.rmStorage('login_phone');
    $api.rmStorage('user_groupid');
    $api.rmStorage('user_temp_pwd');
    $api.rmStorage('login_flg');
    $api.rmStorage('upgrade_status');
    $api.rmStorage('user_qq');
    $api.rmStorage('user_order');
};
//避免有时候clearCacheExUserInfo操作，取不到值 前缀可能要改
function getUid() {
    return 'appkd_' + getDeviceId();
};
//自定义提示框
function mAlert(_msg, _btn_val, _callback, mustLogin, mustUpgrade) {
    popWin(_msg, '', mustLogin, mustUpgrade);
};
//自定义进度条
function mShowProgress(_txt, _modal, _style, _animationType, _title) {
    api.showProgress({
        style: _style ? _style : OT.progress_style,
        animationType: _animationType ? _animationType : OT.progress_animationType,
        title: _title ? _title : OT.progress_title,
        text: _txt,
        modal: _modal ? _modal : false
    });
};
//自定义toast
function mToast(_msg, _location, _duration) {
    api.toast({
        msg: _msg,
        duration: _duration ? _duration : OT.toast_duration,
        location: _location ? _location : OT.toast_location,
    });
};
//确认frame
function mConfirm(param) {
    api.openFrame({
        name: 'confirmFrame',
        url: 'widget://html/confirm_frame.html',
        pageParam: param
    });
};
function mHint(param) {
    api.openFrame({
        name: 'alertFrame',
        url: 'widget://html/alert_frame.html',
        pageParam: param
    });
};
//记录用户行为
function mLog(msg) {};
//自定义ajax调用
function mAjax(_url, callback_ok, callback_err, _data, _files, _method, _timeout, _dataType, _returnAll) {
    if (_url == '') return;
    _url = _url.replace(/^http:/, 'https:');
    if (getStorageByName('httpMode') == 1) { //HTTP调试模式,不要注销
        _url = _url.replace(/^https:/, 'http:');
    }
    //_url = _url.replace('www.acc5.com','192.168.31.42').replace(/^https:/,'http:');//本地测试
    _data = _data || {}
    var progress = _data.showProgress !== false;
    progress && mShowProgress(null, null, null, null, _data && _data.showProgressTitle);
    //_data.deviceid=api.deviceId;
    _data.auth_code = getAuthCode();
    _data.appVer = '1.9.31';//api.appVersion; //最低版本号1.7.5（1705），否则会出现升级版本提示，用于测试时固定为1.7.5
    _data.os = api.systemType;
    _data.osVer = api.systemVersion;
    _data.dName = api.deviceName;
    _data.dModel = api.deviceModel;
    _data.appId = appId;
    var wrapData = {}
    wrapData.values = _data;
    if (_files) {
        wrapData.files = _files;
    }
    api.ajax({
        url: _url,
        method: _method || 'post',
        timeout: 30,
        dataType: 'json',
        returnAll: false,
        data: wrapData
    }, function(ret, err) {
        progress && api.hideProgress();
        api.refreshHeaderLoadDone();
        if (ret && ret.retcode == 200) {
            ret = JSON.parse(authcode(ret.result, 'DECODE', _k4code_))
        }
        //console.log(api.winName +" && "+api.frameName+" && "+_url+"==>\n param==>"+JSON.stringify(_data)+"\n  args=>"+JSON.stringify(arguments)+"\n  ret=>"+JSON.stringify(ret))
        if (ret) {
            if ((filterUrl(_url) && ret.code == 403) || ret.code == 402) {
                if (ret.code == 402) {
                    mAlert(ret.msg_text);
                    $api.setStorage('user_experience_time', 0);
                    return;
                }
                mAjax(OT.url_userinfo, function() {
                    if (ret.code == 200) {
                        $api.setStorage('userid', ret.data.userid);
                        $api.setStorage('username', ret.data.username);
                        $api.setStorage('login_phone', ret.data.login_phone);
                        $api.setStorage('user_groupid', ret.data.user_groupid);
                        $api.setStorage('user_experience_time', ret.data.user_experience_time);
                        $api.setStorage('nick', ret.data.nickname || ret.data.nick);
                        $api.setStorage('user_avatar', ret.data.user_avatar);
                        $api.setStorage('login_flg', 1);
                        $api.setStorage('videoPath', ret.data.videoPath);
                        $api.setStorage('upgrade_status', ret.data.upgrade_status);
                        $api.setStorage('user_qq',ret.data.user_qq);
                        $api.setStorage('user_order',ret.data.user_order);
                        ret.data.debug == 1 && clearCacheExUserInfo();
                        getFavorData();
                    } else {
                        mAlert(ret.msg_text, '','',true);
                    }
                }, function() {
                    api.closeWidget();
                }, {});
            }
            if (callback_ok) callback_ok(ret);
        } else {
            al(_url + " 出错信息=>" + JSON.stringify(err));
            if (err.code == 3) { //0、连接错误 1、超时 2、授权错误 3、数据类型错误
                err.body = err.body + " &Post Data=" + JSON.stringify(_data); //把出错的参数也传回去
                feedbackBug(err, _url);
                //mAlert("出错啦！");
            } else {
                if (_data.alertAllError !== false) { //网络错误时是否弹出提示，默认弹出
                    var msg = '请检查您的网络是否正常'; //err.code!=3 ? '请检查您的网络是否正常' : err.msg
                    if (_url == OT.url_postpic) {
                        msg = "上传失败，" + msg;
                    }
                    mAlert(msg);
                }
            }
            if (callback_err) callback_err(err);
        }
    });
};
//重复登录时再次调用会请求
function filterUrl(_url) {
    if (_url.indexOf('app_course_view') != -1) return true;
    if (_url.indexOf('app_ask') != -1) return true;
    if (_url.indexOf('app_ask_detail') != -1) return true;
    return false;
};
//反馈上报
function feedback(config) {
    var code = (getStorageByName('userid') || 0) + '-' + new Date().getTime(); //0表示未登录
    config.show && mAlert(config.msg + "[" + code + "]");
    config.loading && mShowProgress(null, null, null, null, "提交中...");
    var data = {
        type: config.type, //9表示后台bug，8表示用户反馈
        subtype: config.subtype, //用户反馈下的子分类
        code: code,
        msg: config.msg,
        url: config.url,
        content: config.content,
        page: config.data, //反馈来源页面的数据
    };
    data.auth_code = getAuthCode();
    data.appVer = api.appVersion;
    data.os = api.systemType;
    data.osVer = api.systemVersion;
    data.dName = api.deviceName;
    data.dModel = api.deviceModel;
    data.appId = appId;
    api.ajax({
        url: OT.url_error_report,
        method: 'post',
        data: {
            values: data
        },
        dataType: "json",
        returnAll: false
    }, function(ret, err) {
        config.loading && api.hideProgress();
        if (ret) {
            config.success && config.success(ret);
        } else {
            config.error && config.error(err);
        }
    });
};
//后台错误
function feedbackBug(err, url) {
    feedback({
        type: 9,
        msg: err.msg,
        content: err.body,
        url: url,
        show: true
    });
};
//用户反馈
function feedbackAdvice(content, type, data, success, error) {
    feedback({
        type: 8,
        subtype: type,
        content: content,
        data: data,
        show: false,
        loading: true,
        success: success,
        error: error
    });
};
//是否仅wifi下载
function getOnlyWifi() {
    var wifi = api.getPrefs({
        sync: true,
        key: 'wifi'
    });
    return wifi != 20; //10是wifi 20是全部
};
//设置仅wifi下载
function setOnlyWifi(only) {
    api.setPrefs({
        key: 'wifi',
        value: only ? 10 : 20
    });
};
//是否点击下载时打开下载中心
function getDownloadOpen() {
    var open = api.getPrefs({
        sync: true,
        key: 'downloadOpen'
    });
    return open != 2;
};
//设置点击下载时是否打开下载中心
function setDownloadOpen(open) {
    api.setPrefs({
        key: 'downloadOpen',
        value: open ? 1 : 2
    });
};
//判断用户权限，看是否可以下载视频
function canDownload() {
    if (getCookie('ioscheck') == 1) return false;
    if ($api.getStorage('login_flg') == 1 && ($api.getStorage('user_groupid') == 19 || $api.getStorage('user_groupid') == 8)) return true;
    return false;
};
//清除所有离线文件
function clearDownload() {
    var manager = api.require('downloadManager');
    manager.query({}, function(ret, err) {
        if (ret && ret.data) {
            for (var i = 0; i < ret.data.length; i++) {
                manager.remove({
                    ids: [ret.data[i].id]
                }, function(ret, err) {

                });
            }
        }
    });
    var dataPath = api.systemType == 'ios' ? OT.dataPathIOS : OT.dataPath;
    var fs = api.require('fs');
    fs.rmdir({
        path: dataPath
    }, function(ret, err) {
        //有时候再次删除会不存在文件夹，使用status为false
        if (ret && ret.status) {
            if (api.winName == 'download') {
                api.execScript({
                    name: 'download',
                    frameName: 'downloadFrame',
                    script: 'initDownloadList();'
                });
            }
        }
        mToast("清除离线成功！");
    });
};
/**
* 打开我的离线
* @param showCourse [boolean] 显示课程还是讲义，默认true
* @param showDownloaded [boolean] 是否展开已下载项，默认true
* @param showDownloading [boolean] 是否展开正在下载项，默认true
*/
function showDLCenter(showCourse, showDownloaded, showDownloading) {
    api.openWin({
        name: 'download',
        url: 'widget://html/downloadCenter.html',
        reload: true,
        pageParam: {
            showDownloaded: showDownloaded,
            showDownloading: showDownloading,
            showCourse: showCourse,
        }
    });
};
//下载视频（分三种情况：0、未下载，1、已经离线，2、已经加入下载列表但未完成）
function mDownload(_url, callbackOK, callbackErr, _title, _savePath, _iconPath, showDL, showStartToast) {
    if ($api.getStorage('user_groupid') == 2) { //体验用户不可下载
        $api.getStorage('upgrade_status') == 1 ? mAlert('请升级权限后再进行操作！[体验用户不能下载！]', '', '', true, true) : mHint({
            msg: "体验用户不可下载！"
        });
        return;
    }
    var manager = api.require('downloadManager');
    manager.query({}, function(ret, err) {
        var data = [];
        if (ret && ret.data) {
            data = ret.data;
        }
        var status = 0; //默认是未下载，1是已经下载完成、2是已经加入下载列表了还未完成（等待、下载、暂停、出错）
        var el;
        for (var i = 0; i < data.length; i++) {
            if (equalIgnoreHttp(data[i].url, _url)) {
                el = data[i];
                status = data[i].status == 3 ? 1 : 2;
                break;
            }
        }
        switch (status) {
            case 0: //未下载
                var dataType = api.systemType == 'ios' ? OT.dataTypeIOS : OT.dataType;
                var dataPath = api.systemType == 'ios' ? OT.dataPathIOS : OT.dataPath;

                var arr = _url.replace(/\?.*$/, '').split("/");
                var filename = arr[arr.length - 1].replace('.mp4', dataType).replace('.m3u8', dataType);
                var mimeType = 'video/x-sgi-movie';
                var iconPath = _iconPath || 'widget://image/course/play.png';
                var network = getOnlyWifi() ? 'wifi' : 'all';
                if (api.systemType == 'ios') { //IOS（特别是Iphone7）
                    if (isHttpIOSDownload()) {
                        _url = _url.replace(/^https:/, "http:")
                    }
                }
                manager.enqueue({
                    url: _url,
                    savePath: dataPath + filename,
                    cache: true,
                    allowResume: true,
                    title: _title,
                    mimeType: mimeType,
                    networkTypes: network,
                    iconPath: iconPath,
                }, function(ret, err) {
                    if (ret.id) {
                        api.execScript({
                            frameName: 'courseDetail_frame',
                            script: 'setTimeout(downloadCheck,8000);'
                        });
                        api.execScript({
                            frameName: 'replayFrame',
                            script: 'setTimeout(videoExistCheck,8000);'
                        });
                    } else if (err) {
                        mToast(err.msg);
                    }
                });
                if (showDL !== false && getDownloadOpen()) { //没下载的，需要判断showDL参数和downloadOpen配置
                    showDLCenter(true, false, true);
                } else {
                    showStartToast !== false && mToast('成功加入下载列表')
                }
                break;
            case 1: //已完成
                (showDL !== false) && showDLCenter(true, true, false); //已经下载了的，只判断showDL参数
                break;
            case 2: //未完成
                manager.resume({
                    id: el.id
                }, function(ret, err) {
                    if (showDL !== false && getDownloadOpen()) { //没下载的，需要判断showDL参数和downloadOpen配置
                        showDLCenter(true, false, true);
                    } else {
                        showStartToast !== false && mToast('成功开始下载')
                    }
                });
                break;
        }
    });
};
//下载讲义
function mDownloadCommon(_url, name, courseTitle, showDL, showStartToast) {
    if ($api.getStorage('user_groupid') == 2) { //体验用户不可下载
        $api.getStorage('upgrade_status') == 1 ? mAlert('请升级权限后再进行操作！[体验用户不能下载！]', '', '', true, true) : mHint({
            msg: "体验用户不可下载！"
        });
        return;
    }
    var manager = api.require('downloadManager');
    manager.query({}, function(ret, err) {
        var data = [];
        if (ret && ret.data) {
            data = ret.data;
        }
        var status = 0; //默认是未下载，1是已经下载完成、2是已经加入下载列表了还未完成（等待、下载、暂停、出错）
        var el;
        for (var i = 0; i < data.length; i++) {
            if (equalIgnoreHttp(decodeURI(data[i].url), _url)) { //因为讲义是编码url后再下载的，所以要先解码
                el = data[i];
                status = data[i].status == 3 ? 1 : 2;
                break;
            }
        }
        switch (status) {
            case 0: //未下载
                var dataType = getFileSuffix(_url);
                var dataPath = api.systemType == 'ios' ? OT.dataPathIOS : OT.dataPath;
                //var filename= _url.indexOf('attname=')!=-1 ? _url.substring(_url.indexOf('attname=')+8) : name;
                var iconPath = 'widget://image/course/' + dataType.substr(0, 3) + '.png'; //可以不用设置
                var network = getOnlyWifi() ? 'wifi' : 'all';
                if (api.systemType == 'ios') { //IOS（特别是Iphone7）
                    if (isHttpIOSDownload()) {
                        _url = _url.replace(/^https:/, "http:")
                    }
                }
                manager.enqueue({
                    url: encodeURI(_url), //有一些要编码后才能下载
                    savePath: dataPath + name, //直接使用传回来的name不用去处理根据url获取filename
                    cache: true,
                    allowResume: true,
                    title: courseTitle, //课程名称，不是文件名称
                    networkTypes: network,
                    iconPath: iconPath,
                }, function(ret, err) {
                    //console.log(JSON.stringify(arguments))
                    if (ret.id) {
                        api.execScript({
                            frameName: 'courseDetail_frame',
                            script: 'setTimeout(downloadCheck,8000);'
                        });
                        api.execScript({
                            frameName: 'replayFrame',
                            script: 'setTimeout(videoExistCheck,8000);'
                        });
                    } else if (err) {
                        mToast(err.msg);
                    }
                });
                if (showDL !== false && getDownloadOpen()) { //没下载的，需要判断showDL参数和downloadOpen配置
                    showDLCenter(false, false, true);
                } else {
                    showStartToast !== false && mToast('成功加入下载列表')
                }
                break;
            case 1: //已完成
                (showDL !== false) && showDLCenter(false, true, false);; //已经下载了的，只判断showDL参数
                break;
            case 2: //未完成
                manager.resume({
                    id: el.id
                }, function(ret, err) {
                    if (showDL !== false && getDownloadOpen()) { //没下载的，需要判断showDL参数和downloadOpen配置
                        showDLCenter(false, false, true);
                    } else {
                        showStartToast !== false && mToast('成功开始下载')
                    }
                });
                break;
        }
    });
};
//获取下载文件所对应的文件名称
function getFileNameByUrl(url, withSuffix) {
    var name = url.replace(/\?.*$/, '').split("/").pop();
    return withSuffix ? name : name.substr(0, name.lastIndexOf('.'));
};
//获取文件后缀
function getFileSuffix(url) {
    var name = url.replace(/\?.*$/, '').split("/").pop();
    return name.substring(name.lastIndexOf('.') + 1).toLowerCase();
};
//后台自动登录
function silenceLogin() {
    if ($api.getStorage('username') != '' && $api.getStorage('user_temp_pwd') != '' && $api.getStorage('username') != 'undefined' && $api.getStorage('user_temp_pwd') != 'undefined') {
        var _data = {
            username: $api.getStorage('username'),
            seccode: '',
            password: $api.getStorage('user_temp_pwd'),
            deviceid: getDeviceId(),
            submit: 'submit'
        }
        mAjax(OT.url_login, function(ret) {
            if (ret.code == 200) {
                $api.setStorage('userid', ret.uid);
                mAjax(OT.url_userinfo, getInfoOK, callbackErr, {
                    uid: ret.uid
                });
            } else  if (ret.code == 504) {
		        mAlert('需要验证手机短信验证码');
	            api.openWin({
	                name: 'login',
	                url: 'widget://html/login.html',
	                reload: true,
	                pageParam: {
	                    needsec: 1
	                }
	            });
		    }else{
		    	gotoLogin('widget://html/login.html');
		    }
        }, callbackErr, _data)
    }
};
//跳转到登录页面
function gotoLogin(_url) {
    getCookie('ioscheck') != 1 && api.openWin({
        name: 'login',
        url: _url,
    });
};
//打开扫描
function gotoScan(_title) {
    api.openWin({
        name: 'scan',
        url: 'scan.html',
        reload: true,
        pageParam: {
            title: _title
        }
    });
};
//调用扫码功能
function openScan() {
    var fns = api.require('FNScanner');
    fns.openScanner({
        sound: 'widget://res/beep.wav',
        autorotation: false,
        saveToAlbum: false,
    }, function(ret, err) {
        if (ret) {
            if (ret.eventType == 'success') {
                scanB4Handle(ret);
            }
        }
    });
};
//扫描失败回调
function scanFail(msg) {
    if (msg) popWin(msg, '扫描结果');
    else popWin('扫描失败，请重新扫描', '扫描结果');
};
//扫描成功回调
function scanB4Handle(ret) {
    try {
        if (ret && ret.content) {
            var obj = ret.content;
            if (obj) {
                //like this http://www.acc5.com/api.php?c=scan&op=binding_user&type=1&k=xxx
                if (obj.indexOf('c=scan') != -1) {
                    api.ajax({
                        url: obj,
                        dataType: 'text',
                        headers: {
                            apicloud: 'acc5.com',
                        }
                    }, function(ret, err) {
                        if (ret) {
                            var result = {};
                            result.content = ret;
                            scanB4Handle(result);
                        } else {
                            if (err.code == 3) { //0、连接错误 1、超时 2、授权错误 3、数据类型错误
                                feedbackBug(err, obj);
                            } else {
                                mAlert('请检查您的网络是否正常');
                            }
                        }
                    });
                    return;
                }
                obj = JSON.parse(authcode(obj.replace('acc5_app:', ''), 'DECODE', _k4code_));
                scanHandle(obj);
            } else {
                scanFail();
            }
        } else {
            scanFail();
        }
    } catch (e) {
        scanFail(JSON.stringify(ret.content));
    }
};
//扫描成功回调
function scanHandle(obj) {
    if (!obj.type) scanFail(JSON.stringify(obj.content));

    if (obj.type == 'login') {
        $api.setStorage('token', obj.token);
        mAjax(OT.url_login, loginOK, callbackErr, {
            'token': obj.token,
            'submit': '1'
        });
    } else if (obj.type == '') {

    } else {}
};
//登录成功处理
function loginOK(ret) {
    //console.log('ret='+JSON.stringify(ret));
    // 此处好像没用，不知道是什么作用？
    if (ret.code != 200 && ret.code != 504 && api.systemType != 'android' && api.frameName != 'login_frame') {
        api.closeWin();
    }
    if (ret.code == 200) {
        $api.setStorage('userid', ret.uid);
        mAjax(OT.url_userinfo, getInfoOK, callbackErr, {
            uid: ret.uid
        });
    } else if (ret.code == 504) {
        mAlert('需要验证手机短信验证码');
        if (api.winName == 'login') {
            loginVM.verify = true;
        } else {
            api.openWin({
                name: 'login',
                url: 'widget://html/login.html',
                reload: true,
                pageParam: {
                    needsec: 1
                }
            });
        }
    } else {
        mAlert(ret.msg_text)
    }
    /* 直接使用返回的消息：mAlert(ret.msg_text)
   	else if(ret.code==404){mAlert('存在');}
	else if(ret.code==501){mAlert('用户名不能为空');}
	else if(ret.code==502){mAlert('登录太频繁,请稍后再试');}
	else if(ret.code==503){mAlert('密码错误');}
	else if(ret.code==505){mAlert('需要先去网站绑定手机');}
	else if(ret.code==506){mAlert('手机验证码错误');}
	else if(ret.code==507){mAlert('令牌过期');}
	else if(ret.code==599){mAlert('未定义错误');}
	else if(ret.code==600){mAlert('用户被锁定');}
	*/
};
//获取用户信息成功返回
function getInfoOK(ret) {
    //console.log('getInfoOK=>'+JSON.stringify(ret));
    if (ret.code == 200) {
        $api.clearStorage();
        $api.setStorage('userid', ret.data.userid);
        $api.setStorage('username', ret.data.username);
        $api.setStorage('login_phone', ret.data.login_phone);
        $api.setStorage('user_groupid', ret.data.user_groupid);
        $api.setStorage('user_experience_time', ret.data.user_experience_time);
        $api.setStorage('nick', ret.data.nickname || ret.data.nick);
        $api.setStorage('user_avatar', ret.data.user_avatar);
        $api.setStorage('login_flg', 1);
        $api.setStorage('videoPath', ret.data.videoPath);
        $api.setStorage('upgrade_status', ret.data.upgrade_status);
        $api.setStorage('user_qq',ret.data.user_qq);
        $api.setStorage('user_order',ret.data.user_order);

        getFavorData();
        api.execScript({
            name: 'root',
            frameName: 'info',
            script: 'initUserInfo();'
        });
        if (api.winName != 'root') {
            api.closeToWin({
                name: 'root'
            });
        }
    } else {
        if (api.systemType != 'android') api.closeWin();// 此处好像没用，不知道是什么作用？获取用户信息失败关闭页面重新登录？
        mAlert('获取用户信息失败！');
    }
};
//重命名视频文件(不保存为mp4格式)
function renameVideo() {
    var fs = api.require('fs');
    var dataType = api.systemType == 'ios' ? OT.dataTypeIOS : OT.dataType;
    var dataPath = api.systemType == 'ios' ? OT.dataPathIOS : OT.dataPath;
    fs.readDir({
        path: dataPath
    }, function(ret, err) {
        if (ret.status) {
            if (ret.data) {
                for (var key in ret.data) {
                    var oldfn = ret.data[key];
                    if (oldfn.indexOf('.mp4') == -1) continue;
                    var newfn = oldfn.replace('.mp4', dataType);
                    fs.renameSync({
                        oldPath: dataPath + oldfn,
                        newPath: dataPath + newfn
                    });
                }
            }
        }
    });
};
// 改名为mp4格式
function getVideoMp4(url) {
    var arr = url.split('/');
    var dataType = api.systemType == 'ios' ? OT.dataTypeIOS : OT.dataType;
    var dataPath = api.systemType == 'ios' ? OT.dataPathIOS : OT.dataPath;
    if (arr[arr.length - 1].indexOf(dataType) != -1) {
        var oldPath = dataPath + arr[arr.length - 1];
        var newPath = oldPath.replace(dataType, '.mp4');
        var fs = api.require('fs');
        var ret = fs.renameSync({
            oldPath: oldPath,
            newPath: newPath
        });
        if (ret.status) {
            url = newPath;
        } else {
            var ret2 = fs.existSync({
                path: newPath
            });
            if (ret2.exist) {
                url = newPath;
            }
        }
    }
    return url;
};
//不区分https和http前缀地比较两个下载url，去掉?后面参数信息
function equalIgnoreHttp(v1, v2) {
    if (v1 && v2) {
        return v1.replace(/^https:/, 'http:').replace(/\?.*$/, '') == v2.replace(/^https:/, 'http:').replace(/\?.*$/, '');
    }
    return false; //参数为空时出现
};
//获取播放器设置(默认播放器改为加速播放器1)
function getMyPlayer() {
    return getStorageByName("myPlayerV2") || 2;
};
//设置播放器
function setMyPlayer(i) {
    $api.setStorage("myPlayerV2", i || 2);
};
//IOS视频下载模式相关方法，1、默认后台 2、http 3、https
function getIOSDownloadMode() {
    return getStorageByName("iosDownloadMode") || 1; //默认是不去管是否是https，后台传来什么就用什么
};
function setIOSDownloadMode(i) {
    $api.setStorage("iosDownloadMode", i || 1);
};
function setHttpsIOSDownload(i) {
    if (i === true) { //https
        i = 3;
    } else if (i === false) { //http
        i = 2
    } else { //后台模式
        i = 1;
    }
    $api.setStorage("iosDownloadMode", i || 1);
};
function isHttpIOSDownload() {
    return getIOSDownloadMode() == 2;
};
function isHttpsIOSDownload() {
    return getIOSDownloadMode() == 3;
};
//获取所有已经播放
function getCoursePlayed() {
    return getStorageByName('coursePlayed');
};
//设置所有已经播放
function setCoursePlayed(played) {
    $api.setStorage('coursePlayed', played);
};
//获取已经播放
function getCoursePlayedById(id) {
    var played = getCoursePlayed();
    return played ? played[id] : null;
};
//设置已经播放
function setCoursePlayedById(id, config, lessones) {
    var lesson = lessones[config.lesson];
    var played = getCoursePlayed() || {};
    if (id == 'download') {
        if (lesson.url.indexOf('?') > -1) {
            var urlSub = lesson.url.substr(lesson.url.indexOf('?') + 1).split('-');
            id = urlSub[0];
            var lessonId = urlSub[1];
            config.lesson = urlSub[2];
            config.chapter = urlSub[3];
            lesson.id = lessonId;
        }
    }
    config.time = config.time || lesson.time || 0; //由于播放前就会设置播放时间，且可能为0，所以不能使用以前的开始时间|| (played[id] && played[id].time)
    played[id] = config;
    setCoursePlayed(played);
    addPlayHistory(lesson, config.time, id); //播放缓存，不用处理id相关
};
//播放记录设置和获取
function getPlayHistory() {
    return getStorageByName('lessonPlayed') || {
        list: [],
        played: {},
    };
};
function setPlayHistory(history) {
    $api.setStorage('lessonPlayed', history);
};
function addPlayHistory(lesson, time, courseId) {
    var history = getPlayHistory();//过滤重复当前id的
    var old = {};
    history.list = history.list.filter(function(v) {
        var urlEqual = equalIgnoreHttp(v.url, lesson.url);
        if (v.id == lesson.id || urlEqual) {
            old = v;
        }
        return v.id != lesson.id && !urlEqual;
    });
    lesson.id = old.id || lesson.id;
    lesson.duration = lesson.duration || old.duration;
    lesson.time = time || old.time;
    lesson.courseId = courseId;
    history.list.push(lesson);
    history.played[getFileNameByUrl(lesson.url)] = time; //不使用id，直接使用播放地址的文件名称
    setPlayHistory(history);
};
//下载对应的课程信息
function getDownloadCourse() {
    return getStorageByName('lessonDownloaded') || {};
};
function setDownloadCourse(data) {
    $api.setStorage('lessonDownloaded', data);
};
function setDownloadLesson(url, course, lesson, index, chapterIndex) {
    //console.log("args="+JSON.stringify(arguments))
    var data = getDownloadCourse();
    data[getFileNameByUrl(url)] = {
        course: {
            id: course && course.id,
            title: course && course.title,
        },
        chapterIndex: chapterIndex,
        chapterTitle: chapterIndex >= 0 ? lesson[chapterIndex].chapter_name : '',
        index: index,
        duration: chapterIndex >= 0 ? lesson[chapterIndex].lesson[index].duration : lesson[index].duration //注:CPA不一样
    };
    //console.log(chapterIndex>=0 ? lesson[chapterIndex].lesson[index].duration : lesson[index].duration)
    setDownloadCourse(data);
};
//获取上一次做题记录
function getPracticePre(id) {
    var all = getStorageByName("practicePre") || {};
    return all[id];
};
//设置上一次做题记录，id=cid+"_"+mid+"_"+sid|eid
function setPracticePre(id, data) {
    var all = getStorageByName("practicePre") || {};
    all[id] = data;
    $api.setStorage("practicePre", all);
};
var lasttime = 0; //用于播放延迟，避免连续播放异常退出
//视频播放
function playVideo(url, title, config, downloaded) {
    var local = {};
    if (downloaded) {
        downloaded.forEach(function(v) {
            local[getFileNameByUrl(v.url)] = v.savePath;
        });
    }
    if (config) {
        var played = getPlayHistory().played;
        config.list.forEach(function(v) {
            v.video = v.url;
            if (downloaded) {
                var temp = local[getFileNameByUrl(v.url)];
                v.video = temp ? getVideoMp4(temp) : v.url; //把url改为video
                temp = null;
            }
            v.time = played[getFileNameByUrl(v.url)] || 0; //过去播放时间  使用lesson_played
        });
        config.time = config.list[config.lesson].time = config.list[config.lesson].time || config.time; //播放开始时间
        setCoursePlayedById(config.id, {
            title: title, //视频名称
            time: config.time || 0, //播放到的时间s
            chapter: config.chapter, //章节index
            lesson: config.lesson, //课时index
        }, config.list); //把列表也传过去，把课时id也缓存对应每一个课时播放时间
        api.execScript({
            name: 'courseDetail',
            script: 'initPlayed();'
        });
    }
    var localUrl = local[getFileNameByUrl(url)];
    if (localUrl) {
        url = getVideoMp4(localUrl);
    }
    var backList = (config && config.list && [].slice.call(config.list)) || [];
    var back = function(ret, rename) { //默认返回改名字，其他集或者自动下一集使用back时不改名字
        if (config && config.id && ret && ret.vindex >= 0) { //播放下载中心的离线列表时没有id
            setCoursePlayedById(config.id, {
                title: backList[ret.vindex] && backList[ret.vindex].title, //视频名称
                time: ret.time, //播放到的时间s
                chapter: config.chapter, //章节index
                lesson: ret.vindex, //课时index
            }, backList); //把列表也传过去，把课时id也缓存对应每一个课时播放时间
            api.execScript({
                name: 'courseDetail',
                script: 'initPlayed();'
            });
            api.execScript({
                name: 'playHistory',
                frameName: 'playHistoryFrame',
                script: 'initHistory();'
            });
        }
        if (config.id == 'download') { //下载中心
            api.execScript({
                name: 'download',
                frameName: 'downloadFrame',
                script: 'initLesssonPlayed();'
            });
        }
        lasttime = new Date().getTime();
        (rename !== false) && renameVideo(); //点击其他集或者自动下一集不改名字
    };
    var now = new Date().getTime();
    var delay = 0; //(now-lasttime)>2500 ? 0 : (2500-(now-lasttime));
    //setTimeout:修复部分手机只能播放一个视频的问题（播放完一个视频后自动到下一个或者播放第二个时app就崩溃）
    setTimeout(function() {
        var player = Number(getMyPlayer());
        switch (player) {
            case 1:
                videoPlayer(url, title, back);
                break;
            case 2:
                vitamioPlayer(url, title, back, config, downloaded);
                break;
            case 3:
                neoPlayer(url, title, back);
                break;
            case 4:
                defaultPlayer(url);
                break;
            default:
                videoPlayer(url, title, back);
        }
    }, delay);
};
//手机自带播放器
function defaultPlayer(url, title, back) {
    api.openVideo({
        url: url
    });
};
//videoPlayer模块播放器
function videoPlayer(url, title, back, failed) {
    var videoPlayer = api.require('videoPlayer');
    videoPlayer.play({
            texts: {
                head: {
                    title: title
                }
            },
            styles: {
                head: {
                    bg: '#00b882',
                    height: 50,
                    titleSize: 16,
                    titleColor: '#fff',
                    backSize: 20,
                    backImg: 'widget//image/player/back_no_circle.png',
                    setSize: 30,
                    setImg: 'widget://image/player/setting.png',
                },
                foot: {
                    bg: '#00b882',
                    height: 50,
                    playSize: 30,
                    playImg: 'widget://image/player/play.png',
                    pauseImg: 'widget://image/player/pause.png',
                    nextSize: 30,
                    nextImg: 'widget://image/player/next.png',
                    timeSize: 14,
                    timeColor: '#fff',
                    sliderImg: 'widget://image/live/bar.png',
                    progressColor: '#fff',
                    progressSelected: '#1eceaa'
                }
            },
            coverImg: 'widget://image/common/live_poster.jpg', //封面图片
            path: url,
            autoPlay: true,
            autorotation: false
        },
        function(ret, err) {
            if (ret) {
                if (ret.eventType == 'back') {
                    back && back();
                } else if (ret.eventType == 'failed') {
                    //failed();
                    mToast("播放失败啦，请重试");
                    videoPlayer.start();
                }
            }
        });
};
//自定义播放器模块
function vitamioPlayer(url, title, back, config, downloaded) {
    if (!config) { //没列表
        config = {
            time: 0,
            lesson: 0,
            list: [{
                video: url,
                title: title
            }],
        }
    }
    //api.systemType!='android' && mToast('正在打开播放器','bottom',50000);
    api.systemType != 'android' && api.setScreenOrientation({ //切换到横屏
        orientation: 'landscape_right'
    });
    var vplayer = api.require('vitamioPlayer');
    vplayer.play({
        videos: config.list,
        vindex: config.lesson,
        autoPlay: 1, //自动播放下一集
        download: $api.getStorage('user_groupid') == 2 ? 0 : 1, //体验用户不可下载
    }, function(ret, err) {
        //console.log("callback="+JSON.stringify({r:ret,h:getStorageByName('lessonPlayed')}))
        if (ret.back) { //返回事件
            back && back(ret); //ret = {time:2,vindex:3,back:true}
            if (api.systemType != 'android') {//播放后IOS横竖屏切换可能会引起部分黑屏或者白屏，延迟一秒好像没有了
            	setTimeout(function(){
	                api.setScreenOrientation({ //切换到竖屏
	                    orientation: 'portrait_up'
	                });
                },1000)
            }
        } else if (ret.download) { //下载
            mDownload(ret.download, function(ret) {}, callbackErr, ret.title, null, null, false); //不显示下载列表
        } else if (ret.next) { //点击播放其他集或者自动下一集
            back && back(ret, false); //不改名
        }
    });
};
//使用neoplayer实现的h5播放器
function neoPlayer(url, title, back) {
    api.openWin({
        name: 'player',
        url: 'player.html',
        pageParam: {
            url: url,
            title: title,
            back: back
        }
    });
};
//弹出alert_frame
function popWin(_msg, _title, mustLogin, mustUpgrade) {
    //alert(api.winWidth+'  '+api.winHeight+'  '+api.screenWidth+'  '+api.screenHeight);
    api.openFrame({
        name: 'alertFrame',
        url: 'widget://html/alert_frame.html',
        reload: true,
        hScrollBarEnabled: false,
        vScrollBarEnabled: false,
        pageParam: {
            title: (_title ? _title : OT.alert_title),
            msg: _msg,
            mustLogin: mustLogin,
            mustUpgrade: mustUpgrade
        },
        rect: {
            x: 0,
            y: 0,
            w: api.winWidth,
            h: api.winHeight
        }
    });
};
//弹出评分页面
function popBox(_title, _item, _cur, _type) {
    api.openFrame({
        name: 'popbox',
        url: 'popbox.html',
        reload: true,
        pageParam: {
            title: _title,
            item: _item,
            cur: _cur,
            type: _type
        },
        rect: {
            x: 0,
            y: 0,
            w: api.winWidth,
            h: api.winHeight
        }
    });
};
//打开通用Header的win
function openWinWithFrame(winName, winTitle, frameName, frameUrl, param, back) {
    api.openWin({
        name: winName,
        url: 'widget://html/header.html',
        pageParam: {
            title: winTitle,
            back: back,
            frame: {
                name: frameName,
                url: frameUrl
            },
            param: param
        }
    });
};
//win里面打开frame的通用方法
function mOpenFrame(header, fn, fnhtml, _data) {
    var headerPos = $api.offset(header);
    y = headerPos.h;
    h = parseInt(api.winHeight - headerPos.h) + 'px';
    api.openFrame({
        name: fn,
        url: fnhtml ? fnhtml : fn + '.html',
        bounces: false,
        hScrollBarEnabled: false,
        vScrollBarEnabled: false,
        pageParam: _data,
        rect: {
            x: 0,
            y: y,
            w: 'auto',
            h: 'auto', //h
        }
    });
};
//back键监听
function addKeybackEventListener(_action) {
    api.addEventListener({
        name: 'keyback'
    }, function(ret, err) {
        if (_action) _action();
        api.setScreenOrientation({ //还原竖屏
            orientation: 'auto_portrait'
        });
        api.closeWin();
    });
};
//右滑返回
function addSwipeBackListener(_action) {
    api.addEventListener({
        name: 'swiperight'
    }, function(ret, err) {
        if (_action) _action();
        api.setScreenOrientation({ //还原竖屏
            orientation: 'auto_portrait'
        });
        api.closeWin();
    });
};
//主菜单对应的左右滑动事件添加处理
function addSwipeEvent(index) {
    //start 左右滑动的时候翻页
    api.addEventListener({
        name: 'swipeleft'
    }, function(ret, err) {
        api.lockSlidPane();
        if (index < 3) {
            api.execScript({
                name: 'root',
                script: 'mainVM.tab(' + (index + 1) + ')'
            });
        }
    });
    api.addEventListener({
        name: 'swiperight'
    }, function(ret, err) {
        if (index > 0) {
            api.execScript({
                name: 'root',
                script: 'mainVM.tab(' + (index - 1) + ')'
            });
        }
        if (index <= 1) { //*待去掉： 暂时不右滑展示
            //api.unlockSlidPane();
        }
    });
};
//监听消息推送
function listenNotice() {
    /*
	var push = api.require('push');
    push.setListener(
        function(ret,err){
            if(ret){
            }
        }
    );
    */
};
//格式化直播时间
function getLiveTime(start_time, end_time) {
    var obj = {};
    var timeStr = '';
    var dateStr = '';
    var t1 = new Date();
    var t2 = new Date();
    t1.setTime(start_time * 1000);
    t2.setTime(end_time * 1000);
    timeStr = t1.getHours() < 10 ? ('0' + t1.getHours()) : t1.getHours()
    timeStr += ':'
    timeStr += t1.getMinutes() < 10 ? ('0' + t1.getMinutes()) : t1.getMinutes()
    timeStr += ' - '
    timeStr += t2.getHours() < 10 ? ('0' + t2.getHours()) : t2.getHours()
    timeStr += ':'
    timeStr += t2.getMinutes() < 10 ? ('0' + t2.getMinutes()) : t2.getMinutes() + ' ';
    dateStr = (t1.getMonth() + 1) + '月' + t1.getDate() + '日';
    obj.timeStr = timeStr;
    obj.dateStr = dateStr;
    return obj;
};
//cookie operation
function setCookie(name, value) {
    var Days = 1;
    var exp = new Date();
    exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
    document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString();
};
//读取cookies
function getCookie(name) {
    var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
    if (arr = document.cookie.match(reg))
        return unescape(arr[2]);
    else
        return null;
};
//获取加密code
function getAuthCode() {
    var deviceid = getDeviceId() + '\t' + $api.getStorage('userid') + '\t' + api.deviceModel + "\t" + appId;
    deviceid = deviceid.replace(/\+/g, '%2B');
    var deviceid_authcode = authcode(deviceid, 'ENCODE', _k4code_, 24 * 60 * 60 * 1000);
    var str = base64_encode(deviceid_authcode)
    setCookie('authCode', str);
    return str;
};
//md5
function authcode(str, operation, key, expiry) {
    var operation = operation ? operation : 'DECODE';
    var key = key ? key : '';
    var expiry = expiry ? expiry : 0;

    var ckey_length = 4;
    key = md5(key);
    // 密匙a会参与加解密
    var keya = md5(key.substr(0, 16));
    // 密匙b会用来做数据完整性验证
    var keyb = md5(key.substr(16, 16));
    // 密匙c用于变化生成的密文
    // IE下不支持substr第一个参数为负数的情况
    if (ckey_length) {
        if (operation == 'DECODE') {
            var keyc = str.substr(0, ckey_length);
        } else {
            var md5_time = md5(microtime());
            var start = md5_time.length - ckey_length;
            var keyc = md5_time.substr(start, ckey_length)
        }
    } else {
        var keyc = '';
    }
    // 参与运算的密匙
    var cryptkey = keya + md5(keya + keyc);
    var strbuf;
    if (operation == 'DECODE') {
        str = str.substr(ckey_length);
        strbuf = base64_decode(str);
        //string = b.toString();
    } else {
        expiry = expiry ? expiry + time() : 0;
        tmpstr = expiry.toString();
        if (tmpstr.length >= 10)
            str = tmpstr.substr(0, 10) + md5(str + keyb).substr(0, 16) + str;
        else {
            var count = 10 - tmpstr.length;
            for (var i = 0; i < count; i++) {
                tmpstr = '0' + tmpstr;
            }
            str = tmpstr + md5(str + keyb).substr(0, 16) + str;
        }
        strbuf = str;
    }
    var box = new Array(256);
    for (var i = 0; i < 256; i++) {
        box[i] = i;
    }
    var rndkey = new Array();
    // 产生密匙簿
    for (var i = 0; i < 256; i++) {
        rndkey[i] = cryptkey.charCodeAt(i % cryptkey.length);
    }
    // 用固定的算法，打乱密匙簿，增加随机性，好像很复杂，实际上对并不会增加密文的强度
    for (var j = i = 0; i < 256; i++) {
        j = (j + box[i] + rndkey[i]) % 256;
        tmp = box[i];
        box[i] = box[j];
        box[j] = tmp;
    }
    // 核心加解密部分
    var s = '';
    //IE下不支持直接通过下标访问字符串的字符，需要先转换为数组
    strbuf = strbuf.split('');
    for (var a = j = i = 0; i < strbuf.length; i++) {
        a = (a + 1) % 256;
        j = (j + box[a]) % 256;
        tmp = box[a];
        box[a] = box[j];
        box[j] = tmp;
        // 从密匙簿得出密匙进行异或，再转成字符
        s += chr(ord(strbuf[i]) ^ (box[(box[a] + box[j]) % 256]));
    }
    if (operation == 'DECODE') {
        if ((s.substr(0, 10) == 0 || s.substr(0, 10) - time() > 0) && s.substr(10, 16) == md5(s.substr(26) + keyb).substr(0, 16)) {
            s = s.substr(26);
        } else {
            s = '';
        }
    } else {
        s = base64_encode(s);
        var regex = new RegExp('=', "g");
        s = s.replace(regex, '');
        s = keyc + s;
    }
    return s;
};
//获取当前时间戳的秒数显示
function time() {
    var unixtime_ms = new Date().getTime();
    return parseInt(unixtime_ms / 1000);
};
function microtime(get_as_float) {
    var unixtime_ms = new Date().getTime();
    var sec = parseInt(unixtime_ms / 1000);
    return get_as_float ? (unixtime_ms / 1000) : (unixtime_ms - (sec * 1000)) / 1000 + ' ' + sec;
};
function chr(s) {
    return String.fromCharCode(s);
};
function ord(s) {
    return s.charCodeAt();
};
function md5(str) {
    return hex_md5(str);
};
var _k4code_ = 'acc5app2015SHdEreIoB6wjpiOZ';

//通用返回事件处理函数
function back() {
    api.setScreenOrientation({
        orientation: 'auto_portrait'
    });
    api.setFullScreen({ //全屏
        fullScreen: false
    });
    api.closeWin();
};
//播放器返回
function playerBack() {
    renameVideo();
    back();
};
//返回主页
function root() {
    api.setScreenOrientation({
        orientation: 'auto_portrait'
    });
    api.closeToWin({
        name: 'root'
    });
};
//是否登录
function isLogined() {
    return getCookie('ioscheck') == 1 || $api.getStorage('login_flg') == 1; //login_flg默认为'0'
};
//是否是套餐用户
function isComboUser() {
    return $api.getStorage('user_groupid') == 8;
};
//是否可注销(体验用户才可以注销)
function isOffAble() {
    return $api.getStorage('user_groupid') < 3;
};
//是否可观看该视频视频
function isPlayAble(courseId, callback) {
    if (getCookie('ioscheck') == 1) { //IOS审核中就直接操作
        callback && callback();
        return;
    }
    if (!isLogined()) {
        mAlert('请登录后再进行操作', '', '', true);
        return;
    }
    var group = $api.getStorage('user_groupid'); //2、体验用户：体验时间内可以观看和下载视频以及观看直播，8、套餐用户：只能观看下载套餐内的，分从业、初级和中级；9、VIP用户：录播都可以，直播有截止时间；19、SVIP：没限制
    if (group == 19) { //SVIP
        callback && callback();
        return;
    }
    if (group == 2) { //体验用户
        var deadline = $api.getStorage('user_experience_time');
        if (deadline <= 0) {
            mAlert('体验时间已过,请升级SVIP再进行操作！<br />[体验截止时间：' + avalon.filters.date($api.getStorage('user_experience_timestamp') * 1000, 'yyyy-MM-dd HH:mm') + ']', '', '', true, true);
            return;
        }
        callback && callback();
        return;
    }
    //其他套餐的都去校验：(group==8 || group==9)vip或者套餐用户、以及其他以后可能添加的用户
    mAjax(OT.url_video_permission, function(ret) {
        //alert(JSON.stringify(ret))
        if (ret.code == 200) {
            if (ret.data.flag == 0) {
                //"[登录账号:"+$api.getStorage('username')+"]"
                mAlert(ret.data.msg, '', '', true, true);
                return;
            }
            callback && callback();
        }
    }, callbackErr, {
        courseid: courseId
    });
};
//是否有真账实操权限
function isOperatable(el, callback) {
    if (!isLogined()) {
        mAlert('请登录后再进行操作', '', '', true);
        return;
    }
    var group = $api.getStorage('user_groupid');
    if (group > 1 || el.is_free == 1) { //非普通用户或者免费
        if (el.do_count >= 3) { //实操练习次数最多三次，后台还没加
            mAlert('不能再做了，最多只能练习三次呀！');
            return;
        }
        callback && callback();
    } else {
        mAlert('暂无权限，请升级套餐后再进行操作！', '', '', true, true);
    }
};
//获取已经下载的列表
function getDownloaded(callback) {
    var manager = api.require('downloadManager');
    manager.query({
        status: 3
    }, function(ret, err) {
        var data = [];
        if (ret) {
            data = ret.data;
        }
        callback && callback(data || []);
    });
};
//初始化视频播放（先检测是否已经离线，否则在线播放）
function initVideoPlayer(url, title, config) {
    getDownloaded(function(data) {
        for (var i = 0; i < data.length; i++) {
            if (equalIgnoreHttp(data[i].url, url)) {
                url = data[i].savePath;
                break;
            }
        }
        playVideo(url, title, config, data);
    });
};
//上传浏览记录
function updateHistory(el) {
    mAjax(OT.url_myHistory, function(ret) {
        if (ret.code == 200) {
            var key = 'history';
            var cache = getCacheById(key);
            if (!cache) {
                cache = {
                    code: 200,
                    data: [el]
                };
            } else {
                var index = -1;
                for (var i = 0, len = cache.data.length; i < len; i++) {
                    if (cache.data[i].id == el.id) {
                        index = i;
                        break;
                    }
                }
                if (index > -1) {
                    cache.data.splice(index, 1);
                }
            }
            cache.data.unshift(el);
            $api.setStorage(key, cache);
        }
    }, callbackErr, {
        uid: $api.getStorage('userid'),
        type: 'myHistory',
        courseid: el.id,
        alertAllError: false,
        showProgress: false,
    });
};
//获取header fixStatus的padding-top
function getHeaderPadding() {
    var y = 0;
    var sysType = api.systemType;
    if (sysType == 'ios') {
        var strSV = api.systemVersion;
        var numSV = parseInt(strSV, 10);
        var fullScreen = api.fullScreen;
        var iOS7StatusBarAppearance = api.iOS7StatusBarAppearance;
        if (numSV >= 7 && !fullScreen && iOS7StatusBarAppearance) {
            y = 20;
        }
    } else if (sysType == 'android') {
        var ver = api.systemVersion;
        ver = parseFloat(ver);
        if (ver >= 4.4) {
            y = 25;
        }
    }
    return y;
};
//unused:使用一个新的frame打开带有input、textarea类型的输入框的header
function openHeaderFrame(type, key) {
    /*
    if(api.systemType=='android'){//只是ios系统才调用
    	return;
    }
    */
    api.openFrame({
        name: 'Header',
        url: 'widget://html/index.header.html',
        bounces: false,
        reload: true,
        softInputMode: 'pan',
        hScrollBarEnabled: false,
        vScrollBarEnabled: false,
        pageParam: {
            type: type,
            key: key
        },
        rect: {
            x: 0,
            y: getHeaderPadding(),
            w: 'auto',
            h: 50
        }
    });
};
function closeHeaderFrame() {
    api.closeFrame({
        name: 'Header'
    });
};
//意见反馈页面
function openAdvice(data) {
    data = data || (window.getFromData && getFromData()) || {}; //getFromData可以在每一个frame页面定义
    data.win = api.winName;
    data.frame = api.frameName;
    openWinWithFrame('advice', '意见反馈', 'adviceFrame', 'widget://html/advice_frame.html', data);
};
//调用图片查看器
function openPB(imgs, index) {
    api.execScript({
        name: api.winName,
        script: 'openImageFull(' + JSON.stringify(imgs) + ',' + index + ');'
    });
};
//打开图片查看器
function openImageFull(imgs, index, showCallback) {
    imgs = imgs || [];
    index = index || 0;
    var padding = getHeaderPadding();
    //alert(padding)
    pb.open({
        images: imgs,
        activeIndex: index,
        placeholderImg: 'widget://res/loading.png',
        bgColor: '#000'
    }, function(ret, err) {
        if (ret) {
            if (ret.eventType == 'show') {
                pbOpen = true;
                showCallback && showCallback();
                api.openFrame({
                    name: 'pbFrame',
                    url: 'pb_frame.html',
                    pageParam: {
                        all: imgs.length,
                        current: index + 1,
                        padding: padding
                    },
                    rect: {
                        x: 0,
                        y: 0,
                        w: 'auto',
                        h: 50 + padding,
                    }
                });
                return;
            }
            ret.eventType == 'change' && setPBFrameIndex(ret.index);
        }
    });
};
//关闭图片查看器
function closeImageFull() {
    pb.close();
    api.closeFrame({
        name: 'pbFrame',
    });
};
function setPBFrameIndex(index) {
    var current = index ? index + 1 : 1;
    api.execScript({
        name: api.winName,
        frameName: 'pbFrame',
        script: 'setCurrent(' + current + ');'
    });
};
//本地图片压缩
function compressImage(url, success) {
    var compress = api.require('imageFilter');
    var config = {
        path: 'fs://upload/cache/',
        quality: 0.5,
    };
    var file = new Date().getTime() + '.jpg'; //jpg速度快，压缩率好；png速度慢，文件反而变大了
    compress.compress({
        img: url,
        quality: config.quality,
        scale: 1,
        save: {
            album: false, //是否保存到系统相册，默认 false
            imgPath: config.path, //(可选项)保存的文件路径,字符串类型，无默认值,不传或传空则不保存，若路径不存在文件夹则创建此目录
            imgName: file, //(可选项)保存的图片名字，支持 png 和 jpg 格式，若不指定格式，则默认 png，字符串类型，无默认值,不传或传空则不保存
        }
    }, function(ret, err) {
        if (ret) {
            //console.log("压缩成功："+url+" => "+(config.path+file));
            success(config.path + file);
            return;
        }
        success(url);
    });
};
//图片上传，可单个或者数组
function uploadFiles(file, callback, checkUpload) {
    if (checkUpload) {
        if (!checkUpload()) {
            mToast("文件数量已经达到最大了")
            return;
        }
    }
    var _data = {
        auth_code: getAuthCode(),
        pid: 0,
        showProgressTitle: "正在上传..."
    };
    compressImage(file, function(file) {
        var _file = {
            file: file
        };
        mAjax(OT.url_postpic, function(ret) {
            if (ret.code == 200) {
                callback(ret);
            } else {
                //alert(JSON.stringify(ret))
                mAlert("上传失败了！请重试")
            }
        }, callbackErr, _data, _file);
    })
};
// 打开UIMediaScanner相册
function openAlbum(max, success, checkUpload) {
    var UIMediaScanner = api.require('UIMediaScanner');
    UIMediaScanner.open({
        type: 'picture',
        column: 3,
        classify: true,
        showPreview: true,
        showBrowser: true,
        max: max || 3,
        sort: {
            key: 'time',
            order: 'desc'
        },
        texts: {
            stateText: '已选择*项',
            cancelText: '取消',
            finishText: '完成'
        },
        styles: {
            bg: '#FFFFFF',
            mark: {
                icon: '', //'widget://image/common/checked.png',
                position: 'bottom_left',
                size: 30
            },
            nav: {
                bg: '#00b882',
                stateColor: '#fff',
                stateSize: 18,
                cancelBg: 'rgba(0,0,0,0)',
                cancelColor: '#fff',
                cancelSize: 18,
                finishBg: 'rgba(0,0,0,0)',
                finishColor: '#fff',
                finishSize: 18
            }
        },
        scrollToBottom: {
            intervalTime: -1,
            anim: true
        },
        exchange: true,
        rotation: true
    }, function(ret) {
        //console.log(JSON.stringify(arguments))
        if (ret.eventType == "confirm") {
            var list = [];
            var ios = api.systemType == 'ios';
            ret.list.some(function(v, k) {
                if (ios) {
                    (function(list, index, path) {
                        var UIMediaScanner = api.require('UIMediaScanner');
                        UIMediaScanner.transPath({
                            path: path
                        }, function(ret, err) {
                            uploadFiles(ret.path, function(ret) {
                                success(ret);
                            });
                        });
                    })(list, k, v.path);
                } else {
                    uploadFiles(v.path, function(ret) {
                        success(ret);
                    }, checkUpload)
                }
            });
        }
    });
};
//不用，改用openAlbum
function openPictures(success) {
    api.getPicture({
        sourceType: 'library',
        mediaValue: 'pic',
        allowEdit: false,
        saveToPhotoAlbum: false
    }, function(ret, err) {
        if (ret) {
            if (ret.data == "")
                return;
            uploadFiles(ret.data, function(ret) {
                success(ret);
            });
        }
    });
};
//IOS版本检查
function iosCheck() {
    setCookie('ioscheck', 0);
    if (api.systemType != 'android') {
        api.ajax({
            url: OT.url_ioscheck + Math.random()
        }, function(ret, err) {
            if (ret) {
                var arr = api.appVersion.split('.');
                var curVer = 0;
                if (arr.length == 3) {
                    curVer = parseInt(arr[0]) * 1000 + parseInt(arr[1]) * 100 + parseInt(arr[2]);
                }
                if (curVer >= ret.minVer) {
                    setCookie('ioscheck', 1);
                    clearCacheExUserInfo();
                }
            } else {
                //5天时间审核，即使拿不到json也不影响审核
                if ((new Date()).getTime() < (1494486332769 + 4 * 24 * 3600 * 1000)) {
                    setCookie('ioscheck', 1);
                }

                if (err.code == 3) { //0、连接错误 1、超时 2、授权错误 3、数据类型错误
                    feedbackBug(err, OT.url_ioscheck);
                } else {
                    mAlert('请检查您的网络是否正常');
                }
            }
        });
    }
};
//检查手机号码是否存在
function checkUserByPhone(phone, exist, no) {
    mAjax(OT.url_check_phone_status + phone, function(ret) {
        if (ret.code == 401) { //已绑定
            exist && exist();
        } else if (ret.code == 200) { //未注册
            no && no();
        } else if (ret.code == 402) {
            mHint({
                msg: '手机号码错误'
            })
        }
    }, function(err) {

    });
};
//格式化数字显示
function formatNumber(v, fix, min, max) {
    fix = Number(fix);
    fix = isNaN(fix) ? 0 : fix; //默认是无小数
    var reg = fix == 0 ? /^(\d|\-)?\d*$/ : new RegExp("^(\\d|\\-)?\\d*(\\.\\d{0," + fix + "})?$"); //要传参数fix：/^\d*(\.\d{0,fix})?$/
    if (!reg.test(v)) {
        v = (v + '').replace(/[^\d\.\-]/g, '') //非数字、小数点、负号的全去掉
        v = v.replace(".", "$").replace(/\./g, "").replace("$", ".") //去掉多余小数点
        v = v.replace("-", "$").replace(/\-/g, "").replace("$", "-") //去掉多余负号
        v = reg.test(v) ? v : (+v).toFixed(fix);
    }
    v = v.replace(/^0{2,}/, '0').replace(/^\-0{2,}/, '-0') //最前面的多个0变成一个
    v = v.replace('-.', '-0.').replace(/^\./, '0.'); //小数点前没有数字的加0
    if (min !== undefined && v < min) {
        return min;
    }
    if (max !== undefined && v > max) {
        return max;
    }
    return v + '';
    //return /^0\d+/g.test(v) ? +v+"" : v+'';//替换开头多个0
};
//格式数值为传统大写数字，默认是两位小数
function formatNumberTraditional(value) {
    var base = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
    var big = ["分", "角", "元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟"];
    if (value == 0 || isNaN(Number(value))) {
        return "零元整"
    }
    value = (value + '00').replace("-", ''); // 乘以100到分, 去掉负号
    var fixedIndex = value.indexOf(".");
    if (fixedIndex != -1) { //有小数位
        value = value.substr(0, fixedIndex + 3).replace(".", "");
    }
    var text = "";
    for (var i = value.length - 1; i >= 0; i--) {
        text += base[value[value.length - 1 - i]] + big[i];
    }
    //.replace(/^壹拾/,'拾') 不用去掉
    text = text.replace("零角零分", "整").replace(/零[角|拾|佰|仟]/g, "零").replace(/零{2,}/, "零").replace("零分", "").replace("零元", '元').replace("零万", '万').replace("零亿", '亿');
    return text;
};
//某个属性数值累加
function countAttr(arr, attr) {
    var count = 0;
    for (var i = 0, len = arr.length; i < len; i++) {
        count += (+arr[i][attr]);
    }
    return count;
};
//优化时间显示格式
function prettyTime(time, from) {
    if (!(time > 0)) { //Invalid Date
        return '';
    }
    var range = (from || new Date()) - time;
    if (range < 0) {
        return;
    }
    var constant = {
        minute: 1000 * 60,
        hour: 1000 * 60 * 60,
        day: 1000 * 60 * 60 * 24,
        week: 1000 * 60 * 60 * 24 * 7,
        month: 1000 * 60 * 60 * 24 * 30,
        year: 1000 * 60 * 60 * 24 * 365,
    };
    var years = range / constant.year;
    if (years >= 1) {
        return parseInt(years) + '年前';
    }
    if (years >= 0.5 && years <= 0.75) {
        return '半年前';
    }

    var months = range / constant.month;
    if (months >= 1) {
        return parseInt(months) + '个月前';
    }
    if (months >= 0.5 && months <= 0.75) {
        return '半个月前';
    }

    var weeks = range / constant.week;
    if (weeks >= 1) {
        return parseInt(weeks) + '周前';
    }

    var days = range / constant.day;
    if (days >= 1) {
        return parseInt(days) + '天前';
    }
    if (days >= 0.5 && days <= 0.75) {
        return '半天前';
    }

    var hours = range / constant.hour;
    if (hours >= 1) {
        return parseInt(hours) + '小时前';
    }
    if (hours >= 0.5 && hours <= 0.75) {
        return '半小时前';
    }

    var minutes = range / constant.minute;
    if (minutes >= 1) {
        return parseInt(minutes) + '分钟前'
    }
    return "刚刚";
};
//字符串html编码
function htmlencode(s) {
    var div = document.createElement('div');
    div.appendChild(document.createTextNode(s));
    return div.innerHTML;
};
//字符串html解码
function htmldecode(s) {
    var div = document.createElement('div');
    div.innerHTML = s;
    return div.innerText || div.textContent;
};
//log输出
function log() {
    debugMode && console.log && console.log.call(console, JSON.stringify(arguments));
};
//alert
function al() {
    debugMode && alert.call(window, JSON.stringify(arguments));
};

/**
 * 进入全屏
 * @param el HTMLElement 需要进入全屏的元素
 */
function fullScreen(el) {
    el = el || document;
    if (el.requestFullscreen) { //ios（没有此方法，undefined）
        el.requestFullscreen();
    } else if (el.webkitRequestFullscreen) { //android
        el.webkitRequestFullscreen(); //有些手机执行没反应，不能全屏
    }
    api.setFullScreen({
        fullScreen: true
    });
    api.setScreenOrientation({
        orientation: 'auto_landscape', //'landscape_right' auto auto_landscape auto_portrait natural
    });
};
//退出全屏
function exitFullScreen() {
    if (document.exitFullscreen) { //ios
        document.exitFullscreen();
    } else if (document.webkitCancelFullScreen) { //android
        document.webkitCancelFullScreen();
    }
    api.setFullScreen({
        fullScreen: false
    });
    api.setScreenOrientation({ //竖屏
        orientation: 'auto_portrait', //'natural'
    });
    //$(window).resize();
};
//网络信息获取
function network(connectionType) {
    var constant = {
        'unknown': {
            name: '未知',
        },
        'ethernet': {
            name: '以太网',
        },
        'wifi': {
            name: 'wifi',
        },
        '2g': {
            name: '2G网络',
        },
        '3g': {
            name: '3G网络',
        },
        '4g': {
            name: '4G网络',
        },
        'none': {
            name: '无网络',
            disbale: true, //不可用
        },
    };
    return constant[connectionType];
};

/**
 * 网络检查
 * @return true:正常可用,false异常不可用
 */
function isNetworkOk() {
    return !network().disable;
};
//应用评分
function openMarketScore() {
    var current = 0; //会计学堂，不同应用需要修改其值
    var android = [
        'com.apicloud.A6999270760613', //会计学堂
        'com.acc5.zhongjivip', //中级会计模考
        'com.acc5.congyevip', //会计从业模考
        'com.acc5.chujivip', //初级会计模考
        'com.acc5.congyebdvip', //会计从业宝典
        'com.acc5.congye', //会计从业宝
        'com.acc5.chuji', //初级会计宝
        'com.acc5.cpa', //CPA
    ]
    var ios = [
        'https://itunes.apple.com/us/app/%E4%BC%9A%E8%AE%A1%E5%AD%A6%E5%A0%82/id1063658190?l=zh&ls=1&mt=8',
        'https://itunes.apple.com/us/app/%E4%B8%AD%E7%BA%A7%E4%BC%9A%E8%AE%A1%E6%A8%A1%E8%80%83/id1187810658?l=zh&ls=1&mt=8',
        'https://itunes.apple.com/us/app/%E4%BC%9A%E8%AE%A1%E4%BB%8E%E4%B8%9A%E6%A8%A1%E8%80%83/id1124371764?l=zh&ls=1&mt=8',
        'https://itunes.apple.com/us/app/%E5%88%9D%E7%BA%A7%E4%BC%9A%E8%AE%A1%E6%A8%A1%E8%80%83/id1207190789?l=zh&ls=1&mt=8',
        'https://itunes.apple.com/us/app/%E4%BC%9A%E8%AE%A1%E4%BB%8E%E4%B8%9A%E5%AE%9D%E5%85%B8/id1187801754?l=zh&ls=1&mt=8',
        'https://itunes.apple.com/us/app/%E4%BC%9A%E8%AE%A1%E4%BB%8E%E4%B8%9A%E5%AE%9D/id1118864231?l=zh&ls=1&mt=8',
        'https://itunes.apple.com/us/app/%E5%88%9D%E7%BA%A7%E4%BC%9A%E8%AE%A1%E5%AE%9D/id1121178231?l=zh&ls=1&mt=8',
        'https://itunes.apple.com/us/app/%E6%B3%A8%E5%86%8C%E4%BC%9A%E8%AE%A1%E5%B8%88-2017cpa%E6%B3%A8%E4%BC%9A%E8%80%83%E8%AF%95%E9%A2%98%E5%BA%93/id1227342915?l=zh&ls=1&mt=8'
    ]
    if (api.systemType == 'android') {
        var vplayer = api.require('vitamioPlayer');
        vplayer.myScore({
            package: android[current]
        });
    } else {
        api.openApp({
            iosUrl: ios[current]
        });
    }
};
//以前使用cid参数是数字，改为使用字符串，不知道TMD有什么毛病，为毛要使用字符串
function getCatByCid(cid) {
    var cat = {
        1: 'cycjfg', //财经法规
        2: 'cykjjc', //会计基础
        3: 'cykjdsh', //会计电算化

        4: 'cjjjf', //初级经济法
        5: 'cjkjsw', //初级会计实务
        6: 'zjkjsw', //中级会计实务
        7: 'zjjjf', //中级经济法
        8: 'zjcwgl', //中级财务管理

        9: 'cpasj', // CPA审计（注册会计师审计）
        10: 'cpakj', //CAP会计（注册会计师会计）
        11: 'cpasf', //CPA税法（注册会计师税法）
        12: 'cpajjf', //CPA经济法（注册会计师经济法）
        13: 'cpacwgl', //CPA财务成管理（注册会计师财务成本管理）
        14: 'cpazlfx', //CPA战略风险（注册会计师公司战略与风险管理）
    };
    return cat[cid];
};
//统一支付接口 param.price的单位是元
function pay(param, success, fail) {
    var now = new Date();
    var expire = new Date(now.getTime() + (1 * 60 * 60 * 1000)); //过期时间为一个小时
    if (param.wxPay) {
        var wxPay = api.require('wxPay');
        wxPay.config({
            /*
		     apiKey: 'wx67b647d7685d6af4',
		     mchId: '1359439102',
		     partnerKey: '274c4dbaa64c6e0029e372c9cff8cbd5',
		     notifyUrl: 'http://app-static.acc5.com/app/wxpay.php'
		     */
        }, function(ret, err) {
            if (ret && ret.status) {
                wxPay.pay({
                    description: param.desc || '',
                    totalFee: (param.price || 0) * 100, //回来是元，微信支付单位是分
                    tradeNo: param.order,
                    spbillCreateIP: '***********',
                    deviceInfo: getDeviceId(),
                    detail: param.detail || '',
                    attach: param.attach || '',
                    feeType: 'CNY',
                    timeStart: now.format("yyyyMMddhhmmss"),
                    timeExpire: expire.format("yyyyMMddhhmmss"),
                    goodsTag: param.productTag || 'WXG',
                    productId: param.productId || '12235413214070356458059',
                    //openId: 'oUpF8uMuAJO_M2pxb1Q9zNjWeS6o'
                }, function(ret1, err1) {
                    if (ret1.status) {
                        success && success(ret1, param);
                    } else {
                        fail && fail(err1, param);
                    }
                });
            }
        });
    } else if (param.aliPay) {
        //alert(param.alipayOrderInfo)
        var aliPay = api.require('aliPay');
        aliPay.payOrder({
            orderInfo: param.alipayOrderInfo
        }, function(ret, err) {
            //console.log(JSON.stringify(arguments))
            if (ret.status || ret.code == 9000) {
                success && success(ret, param);
            } else {
                fail && fail(err, param);
            }
        });
    }
};
//一键加群，idkey不是QQ群号
function addQQGroup(idkey){
	api.openWin({
        name: 'qqGroup',
        url: 'http://shang.qq.com/wpa/qunwpa?idkey='+idkey
    });
};
//获取[min,max]的随机整数，max不加1取不到max
function getRandom(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
};
//隐藏全数字字符串的中间数字，只留前后两位
function hiddenMiddleNum(n){
	return String(n).replace(/^(\d{2})(\d+)(\d{2})$/,"$1****$3");
};
//设置api.deviceId，有些设备没有或获取不到deviceId，给默认一个
function getDeviceId(){
	var data = api.deviceId.replace("-",'').replace('0','');//00000-000-000这类的算获取失败的
	if(data){
		return api.deviceId;
	}else{
		data = api.getPrefs({
		    sync: true,
		    key: 'deviceId'
		});
		if(data){
			return data;
		}
		var path = 'fs://data/com.md.android.info/config.txt';
		data = api.readFile({
		    sync: true,
		    path: path
		});
		if(!data){
			data = new Date().getTime()+'-'+getRandom(1,100000)+'-'+getRandom(100,100000);
			api.writeFile({
			    path: path,
			    data: data
			}, function(ret, err) {
			    if (ret.status) {
			        console.log("保存成功")
			    } else {
					console.log("保存失败")
			    }
			});
		}
		api.setPrefs({
	        key:'deviceId',
	        value:data
        });
		return data;
	}
};
//左填充方法
function leftPad(str, len, ch) {
    var cache = [
        '',
        ' ',
        '  ',
        '   ',
        '    ',
        '     ',
        '      ',
        '       ',
        '        ',
        '         '
    ];
    str = str + '';
    len = len - str.length;
    if (len <= 0) return str;
    if (!ch && ch !== 0) ch = ' ';
    ch = ch + '';
    if (ch === ' ' && len < 10) return cache[len] + str;
    var pad = '';
    while (true) {
        if (len & 1) pad += ch;
        len >>= 1;
        if (len) ch += ch;
        else break;
    }
    return pad + str;
};
//Polyfill Array.prototype.indexOf
if (!Array.prototype.indexOf) {
    Array.prototype.indexOf = function(searchElement, fromIndex) {
        var k;
        if (this == null) {
            throw new TypeError('"this" is null or not defined');
        }
        var o = Object(this);
        var len = o.length >>> 0;
        if (len === 0) {
            return -1;
        }
        var n = fromIndex | 0;
        if (n >= len) {
            return -1;
        }
        k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);
        while (k < len) {
            if (k in o && o[k] === searchElement) {
                return k;
            }
            k++;
        }
        return -1;
    };
}
if (!Array.prototype.findIndex) {
  Object.defineProperty(Array.prototype, 'findIndex', {
    value: function(predicate) {
      if (this == null) {
        throw new TypeError('"this" is null or not defined');
      }
      var o = Object(this);
      var len = o.length >>> 0;
      if (typeof predicate !== 'function') {
        throw new TypeError('predicate must be a function');
      }
      var thisArg = arguments[1];
      var k = 0;
      while (k < len) {
        var kValue = o[k];
        if (predicate.call(thisArg, kValue, k, o)) {
          return k;
        }
        k++;
      }
      return -1;
    }
  });
}